/*
   Innovative Centre - Main JavaScript
   Author: Augment Agent
   Version: 1.0
*/

document.addEventListener('DOMContentLoaded', function() {
    // Colors are now defined in CSS variables in style.css
    // This ensures consistent color application across the site
    const root = document.documentElement;

    // We can still access and modify CSS variables if needed
    const primaryColor = getComputedStyle(root).getPropertyValue('--primary-color').trim();
    const secondaryColor = getComputedStyle(root).getPropertyValue('--secondary-color').trim();

    // Initialize countdown timer
    initCountdown();

    // Initialize form submissions
    initFormSubmissions();

    // Initialize Team Carousel
    initTeamCarousel();

    // Initialize Partners Carousel
    initPartnersCarousel();

    // Initialize Results Carousel
    initResultsCarousel();

    // Initialize phone number inputs
    initPhoneInputs();

    // Initialize video testimonials
    initVideoTestimonials();

    // Add smooth scrolling to all links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            if(this.getAttribute('href') !== '#') {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (document.body.classList.contains('menu-open')) {
                        document.querySelector('.main-nav').classList.remove('active');
                        document.body.classList.remove('menu-open');
                    }
                }
            }
        });
    });
    // Initialize AOS animation library
    AOS.init({
        duration: 800,
        easing: 'ease',
        once: true,
        offset: 100
    });

    // Header scroll effect with dynamic logo change
    const header = document.querySelector('.header');
    const logoImg = document.querySelector('.logo img');

    // Preload both logo images to ensure smooth transition
    if (logoImg) {
        const whiteLogoSrc = logoImg.getAttribute('data-logo-white');
        const blueLogoSrc = logoImg.getAttribute('data-logo-blue');

        if (whiteLogoSrc && blueLogoSrc) {
            // Preload both images
            const preloadWhiteLogo = new Image();
            preloadWhiteLogo.src = whiteLogoSrc;

            const preloadBlueLogo = new Image();
            preloadBlueLogo.src = blueLogoSrc;
        }
    }

    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');

            // Change to blue logo when scrolled
            if (logoImg) {
                const blueLogoSrc = logoImg.getAttribute('data-logo-blue');
                if (blueLogoSrc && !logoImg.src.includes(blueLogoSrc.split('/').pop())) {
                    // Add a small fade effect during transition
                    logoImg.style.opacity = '0.5';
                    setTimeout(() => {
                        logoImg.src = blueLogoSrc;
                        logoImg.style.opacity = '1';
                    }, 150);
                }
            }
        } else {
            header.classList.remove('scrolled');

            // Change back to white logo at the top
            if (logoImg) {
                const whiteLogoSrc = logoImg.getAttribute('data-logo-white');
                if (whiteLogoSrc && !logoImg.src.includes(whiteLogoSrc.split('/').pop())) {
                    // Add a small fade effect during transition
                    logoImg.style.opacity = '0.5';
                    setTimeout(() => {
                        logoImg.src = whiteLogoSrc;
                        logoImg.style.opacity = '1';
                    }, 150);
                }
            }
        }
    });

    // Check scroll position on page load to set the correct logo
    if (window.scrollY > 50) {
        header.classList.add('scrolled');
        if (logoImg) {
            const blueLogoSrc = logoImg.getAttribute('data-logo-blue');
            if (blueLogoSrc) {
                // No need for transition on initial load
                logoImg.src = blueLogoSrc;
            }
        }
    } else {
        // Ensure white logo is shown at the top
        if (logoImg) {
            const whiteLogoSrc = logoImg.getAttribute('data-logo-white');
            if (whiteLogoSrc) {
                logoImg.src = whiteLogoSrc;
            }
        }
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    mobileMenuToggle.addEventListener('click', function() {
        mainNav.classList.toggle('active');
        document.body.classList.toggle('menu-open');
    });

    // Mobile dropdown toggle
    const dropdownItems = document.querySelectorAll('.dropdown');

    dropdownItems.forEach(item => {
        const link = item.querySelector('.nav-link');

        link.addEventListener('click', function(e) {
            if (window.innerWidth < 992) {
                e.preventDefault();
                item.classList.toggle('active');
            }
        });
    });

    // FAQ accordion
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', function() {
            // Close all other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item with animation
            if (item.classList.contains('active')) {
                item.classList.remove('active');
            } else {
                // Add a small delay for better visual effect
                setTimeout(() => {
                    item.classList.add('active');
                }, 50);
            }
        });
    });

    // Add hover effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
        });
    });

    // Testimonials slider
    const testimonialItems = document.querySelectorAll('.testimonial-item');
    const testimonialDots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.testimonial-prev');
    const nextBtn = document.querySelector('.testimonial-next');
    let currentTestimonial = 0;

    function showTestimonial(index) {
        // Hide all testimonials
        testimonialItems.forEach(item => {
            item.classList.remove('active');
        });

        // Remove active class from all dots
        testimonialDots.forEach(dot => {
            dot.classList.remove('active');
        });

        // Show the selected testimonial and activate the corresponding dot
        testimonialItems[index].classList.add('active');
        testimonialDots[index].classList.add('active');
        currentTestimonial = index;
    }

    // Next button click
    nextBtn.addEventListener('click', function() {
        currentTestimonial = (currentTestimonial + 1) % testimonialItems.length;
        showTestimonial(currentTestimonial);
    });

    // Previous button click
    prevBtn.addEventListener('click', function() {
        currentTestimonial = (currentTestimonial - 1 + testimonialItems.length) % testimonialItems.length;
        showTestimonial(currentTestimonial);
    });

    // Dot clicks
    testimonialDots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            showTestimonial(index);
        });
    });

    // Auto-rotate testimonials every 5 seconds
    setInterval(function() {
        currentTestimonial = (currentTestimonial + 1) % testimonialItems.length;
        showTestimonial(currentTestimonial);
    }, 5000);

    // Back to top button
    const backToTopBtn = document.querySelector('.back-to-top');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('active');
        } else {
            backToTopBtn.classList.remove('active');
        }
    });

    backToTopBtn.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Countdown timer function
    function initCountdown() {
        // Set the countdown date (3 days from now)
        const countdownDate = new Date();
        countdownDate.setDate(countdownDate.getDate() + 3);

        // Update the countdown every second
        const countdownTimer = setInterval(function() {
            // Get current date and time
            const now = new Date().getTime();

            // Find the distance between now and the countdown date
            const distance = countdownDate - now;

            // Time calculations for days, hours, minutes and seconds
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Display the result
            if (document.getElementById('countdown-days')) {
                document.getElementById('countdown-days').textContent = days;
                document.getElementById('countdown-hours').textContent = hours;
                document.getElementById('countdown-minutes').textContent = minutes;
                document.getElementById('countdown-seconds').textContent = seconds;
            }

            // If the countdown is finished, clear the interval
            if (distance < 0) {
                clearInterval(countdownTimer);
                if (document.getElementById('countdown')) {
                    document.getElementById('countdown').innerHTML = "<p>Offer has expired!</p>";
                }
            }
        }, 1000);
    }

    // Form submissions
    function initFormSubmissions() {
        // Lead form submission
        const leadForm = document.getElementById('lead-form');
        if (leadForm) {
            leadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(leadForm);
                const formValues = {};
                for (let [key, value] of formData.entries()) {
                    formValues[key] = value;
                }

                // Here you would normally send the data to your server
                // For demo purposes, we'll just show a success message
                leadForm.innerHTML = `
                    <div class="form-success">
                        <i class="fas fa-check-circle"></i>
                        <h3>Thank You!</h3>
                        <p>Your information has been submitted successfully. One of our education consultants will contact you shortly.</p>
                    </div>
                `;

                // Log the form data to console (for demo purposes)
                console.log('Lead form submitted:', formValues);
            });
        }

        // Contact form submission
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(contactForm);
                const formValues = {};
                for (let [key, value] of formData.entries()) {
                    formValues[key] = value;
                }

                // Here you would normally send the data to your server
                // For demo purposes, we'll just show a success message
                contactForm.innerHTML = `
                    <div class="form-success">
                        <i class="fas fa-check-circle"></i>
                        <h3>Message Sent!</h3>
                        <p>Thank you for contacting us. We will get back to you as soon as possible.</p>
                    </div>
                `;

                // Log the form data to console (for demo purposes)
                console.log('Contact form submitted:', formValues);
            });
        }
    }

    // Video modal
    const videoBtn = document.querySelector('.btn-video');

    if (videoBtn) {
        videoBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const videoUrl = this.getAttribute('href');

            // Create modal
            const modal = document.createElement('div');
            modal.className = 'video-modal';
            modal.innerHTML = `
                <div class="video-modal-content compact-video">
                    <span class="video-modal-close">&times;</span>
                    <iframe width="100%" height="100%" src="${videoUrl.replace('watch?v=', 'embed/')}?autoplay=1" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';

            // Close modal
            const closeBtn = modal.querySelector('.video-modal-close');
            closeBtn.addEventListener('click', function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            });

            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                }
            });
        });
    }

    // Add video modal styles
    const style = document.createElement('style');
    style.textContent = `
        .video-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7); /* More transparent background */
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-modal-content {
            position: relative;
            width: 90%;
            max-width: 900px;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }

        /* Compact video style */
        .compact-video {
            width: 70%; /* Smaller width */
            max-width: 700px;
            opacity: 0.9; /* Slight transparency */
        }

        .video-modal-content iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .video-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: #fff;
            font-size: 30px;
            cursor: pointer;
        }

        body.menu-open {
            overflow: hidden;
        }
    `;
    document.head.appendChild(style);

    // Initialize Team Carousel function
    function initTeamCarousel() {
        // Initialize Swiper
        const teamCarousel = new Swiper('.team-carousel', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                640: {
                    slidesPerView: 1,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
        });

        // Category filtering
        const categoryButtons = document.querySelectorAll('.category-btn');

        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                categoryButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const category = this.getAttribute('data-category');

                // Filter slides based on category
                if (category === 'all') {
                    // Show all slides
                    document.querySelectorAll('.swiper-slide').forEach(slide => {
                        slide.style.display = 'block';
                    });
                } else {
                    // Show only slides with matching category
                    document.querySelectorAll('.swiper-slide').forEach(slide => {
                        if (slide.getAttribute('data-category') === category) {
                            slide.style.display = 'block';
                        } else {
                            slide.style.display = 'none';
                        }
                    });
                }

                // Update Swiper after filtering
                teamCarousel.update();
                teamCarousel.slideTo(0);
            });
        });
    }
    // Initialize Partners Carousel function
    function initPartnersCarousel() {
        // Initialize Swiper
        const partnersCarousel = new Swiper('.partners-carousel', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 1500,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                },
            },
        });
    }

    // Initialize Results Carousel function
    function initResultsCarousel() {
        // Initialize Swiper for IELTS Results
        const resultsCarousel = new Swiper('.results-carousel', {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            speed: 800,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                480: {
                    slidesPerView: 2,
                    spaceBetween: 15,
                },
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                },
                1200: {
                    slidesPerView: 5,
                    spaceBetween: 20,
                },
            },
            on: {
                init: function() {
                    console.log('Results carousel initialized');
                }
            }
        });
    }

    // Initialize phone number inputs with country flags
    function initPhoneInputs() {
        const phoneInputs = document.querySelectorAll('input[type="tel"]');

        phoneInputs.forEach(input => {
            // Add phone-input class to ensure styling is applied
            input.classList.add('phone-input');

            // Initialize the international telephone input
            const iti = window.intlTelInput(input, {
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.js",
                initialCountry: "uz",
                preferredCountries: ["uz", "ru", "us", "gb"],
                separateDialCode: true,
                autoPlaceholder: "polite",
                formatOnDisplay: true
            });

            // Add input event listener to limit the number of digits based on country
            input.addEventListener('input', function() {
                const countryData = iti.getSelectedCountryData();
                const countryCode = countryData.dialCode;

                // Get the current input value without the country code
                let inputValue = input.value.replace(/\D/g, '');

                // Set max length based on country code
                let maxLength = 10; // Default
                if (countryCode === '998') { // Uzbekistan
                    maxLength = 9;
                } else if (countryCode === '7') { // Russia
                    maxLength = 10;
                } else if (countryCode === '1') { // US
                    maxLength = 10;
                } else if (countryCode === '44') { // UK
                    maxLength = 10;
                }

                // Truncate if longer than max length
                if (inputValue.length > maxLength) {
                    inputValue = inputValue.substring(0, maxLength);
                }

                // Format Uzbekistan numbers in the specific format: XX-XXX-XX-XX
                if (countryCode === '998' && inputValue.length > 0) {
                    let formattedNumber = '';

                    // Format: XX-XXX-XX-XX
                    if (inputValue.length > 0) {
                        formattedNumber += inputValue.substring(0, Math.min(2, inputValue.length));
                    }

                    if (inputValue.length > 2) {
                        formattedNumber += '-' + inputValue.substring(2, Math.min(5, inputValue.length));
                    }

                    if (inputValue.length > 5) {
                        formattedNumber += '-' + inputValue.substring(5, Math.min(7, inputValue.length));
                    }

                    if (inputValue.length > 7) {
                        formattedNumber += '-' + inputValue.substring(7, Math.min(9, inputValue.length));
                    }

                    // Set the formatted number
                    setTimeout(() => {
                        iti.setNumber("+" + countryCode);
                        input.value = formattedNumber;
                    }, 0);
                } else {
                    // For other countries, use the library's default formatting
                    iti.setNumber("+" + countryCode + inputValue);
                }
            });

            // Add event listener for country change
            input.addEventListener('countrychange', function() {
                // Reset the input value when country changes
                input.value = '';
            });

            // Store the instance for later use
            input.iti = iti;

            // Add some custom styling for the phone input
            const phoneInputStyle = document.createElement('style');
            phoneInputStyle.textContent = `
                .iti {
                    width: 100%;
                }
                .iti__flag-container {
                    display: flex;
                    align-items: center;
                }
                .iti__selected-flag {
                    padding: 0 12px 0 12px;
                    background-color: var(--bg-gray);
                    border-right: 1px solid var(--border-color);
                    transition: var(--transition);
                }
                .iti__selected-flag:hover {
                    background-color: var(--bg-light);
                }
                input.error {
                    border-color: #e74c3c !important;
                    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
                }
                .iti__country-list {
                    background-color: var(--bg-white);
                    border: 1px solid var(--border-color);
                    box-shadow: var(--shadow);
                    border-radius: 5px;
                }
                .iti__country.iti__highlight {
                    background-color: var(--bg-light);
                }
                /* Make all text black */
                .iti__country-name,
                .iti__dial-code,
                .iti__selected-dial-code,
                input[type="tel"] {
                    color: var(--text-color) !important;
                }
                .iti__country {
                    color: var(--text-color) !important;
                }
                /* Move placeholder text to the right */
                input[type="tel"]::placeholder {
                    padding-left: 15px;
                    color: var(--text-lighter) !important;
                    opacity: 1;
                }
                /* Adjust input padding */
                .phone-input {
                    padding-left: 100px !important;
                    text-indent: 15px;
                }
                /* Add subtle animation on focus */
                .phone-input:focus {
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
                    transition: var(--transition);
                }
            `;
            document.head.appendChild(phoneInputStyle);

            // Add validation on form submit
            const form = input.closest('form');
            if (form) {
                const originalSubmitHandler = form.onsubmit;
                form.onsubmit = function(e) {
                    if (!iti.isValidNumber()) {
                        e.preventDefault();
                        input.classList.add('error');

                        // Show error message
                        let errorMsg = document.createElement('div');
                        errorMsg.className = 'phone-error-msg';
                        errorMsg.textContent = 'Please enter a valid phone number';
                        errorMsg.style.color = 'red';
                        errorMsg.style.fontSize = '12px';
                        errorMsg.style.marginTop = '5px';

                        // Remove any existing error message
                        const existingError = input.parentNode.querySelector('.phone-error-msg');
                        if (existingError) {
                            input.parentNode.removeChild(existingError);
                        }

                        input.parentNode.appendChild(errorMsg);
                        return false;
                    } else {
                        // Remove error styling if valid
                        input.classList.remove('error');
                        const existingError = input.parentNode.querySelector('.phone-error-msg');
                        if (existingError) {
                            input.parentNode.removeChild(existingError);
                        }
                    }

                    // If we have an original handler, call it
                    if (typeof originalSubmitHandler === 'function') {
                        return originalSubmitHandler(e);
                    }
                };
            }
        });
    }

    // Initialize video testimonials
    function initVideoTestimonials() {
        const videoItems = document.querySelectorAll('.video-testimonial-item');

        // Sample video URLs - in a real implementation, these would be actual student testimonial videos
        const videoUrls = [
            'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URLs
            'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'https://www.youtube.com/embed/dQw4w9WgXcQ'
        ];

        videoItems.forEach((item, index) => {
            item.addEventListener('click', function() {
                // Create video modal
                const modal = document.createElement('div');
                modal.className = 'video-modal';
                modal.innerHTML = `
                    <div class="video-modal-content compact-video">
                        <span class="video-modal-close">&times;</span>
                        <iframe width="100%" height="100%" src="${videoUrls[index]}?autoplay=1" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // Close modal
                const closeBtn = modal.querySelector('.video-modal-close');
                closeBtn.addEventListener('click', function() {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                });

                // Close modal when clicking outside
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                        document.body.style.overflow = '';
                    }
                });
            });
        });

        // Add video modal styles if not already added
        if (!document.querySelector('style[data-id="video-modal-styles"]')) {
            const videoModalStyles = document.createElement('style');
            videoModalStyles.setAttribute('data-id', 'video-modal-styles');
            videoModalStyles.textContent = `
                .video-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(5px);
                }

                .video-modal-content {
                    position: relative;
                    width: 90%;
                    max-width: 900px;
                    height: 0;
                    padding-bottom: 56.25%; /* 16:9 aspect ratio */
                    box-shadow: var(--shadow-lg);
                    border-radius: 10px;
                    overflow: hidden;
                }

                .compact-video {
                    width: 70%;
                    max-width: 700px;
                    opacity: 0.95;
                    transition: var(--transition);
                }

                .video-modal-content iframe {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: none;
                }

                .video-modal-close {
                    position: absolute;
                    top: -40px;
                    right: 0;
                    color: var(--text-white);
                    font-size: 30px;
                    cursor: pointer;
                    transition: var(--transition);
                }

                .video-modal-close:hover {
                    color: var(--secondary-color);
                    transform: scale(1.1);
                }
            `;
            document.head.appendChild(videoModalStyles);
        }
    }
});