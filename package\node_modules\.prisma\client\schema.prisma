generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Stage {
  EARLY
  MIDDLE
  LATE
}

enum Language {
  RUSSIAN
  UZBEK
  MIXED
}

enum UserRole {
  ADMIN
  RECEPTION
  TEACHER
  STUDENT
}

enum StudentStatus {
  INQUIRY
  LEAD
  ENROLLED
  GRADUATED
  DROPPED
  SUSPENDED
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String // Hashed with bcrypt
  role              UserRole  @default(STUDENT)
  name              String
  isActive          Boolean   @default(true)
  lastLoginAt       DateTime?
  failedAttempts    Int       @default(0)
  lastFailedAttempt DateTime?
  isLocked          Boolean   @default(false)
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?
  refreshToken      String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  auditLogs      AuditLog[]
  teacherProfile Teacher?
  studentProfile Student?
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  action    String // CREATE, UPDATE, DELETE, LOGIN, LOGOUT
  entity    String // User, Student, Teacher, Class, etc.
  entityId  String
  oldData   Json?
  newData   Json?
  ipAddress String
  userAgent String
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([entity, entityId])
  @@index([createdAt])
}

model Teacher {
  id               String       @id @default(cuid())
  userId           String?      @unique
  user             User?        @relation(fields: [userId], references: [id])
  name             String
  email            String       @unique
  phone            String
  subjects         String[]
  qualifications   String[]
  joinDate         DateTime
  status           String       @default("ACTIVE")
  salary           Float?
  address          String?
  dateOfBirth      DateTime?
  photoUrl         String?
  emergencyContact String?
  classes          Class[]
  attendances      Attendance[]
  grades           Grade[]
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
}

model Student {
  id               String        @id @default(cuid())
  userId           String?       @unique
  user             User?         @relation(fields: [userId], references: [id])
  name             String
  phone            String
  joinDate         DateTime
  paymentStatus    String        @default("UNPAID")
  status           StudentStatus @default(INQUIRY)
  parentName       String?
  parentPhone      String?
  emergencyContact String?
  photoUrl         String?
  address          String?
  dateOfBirth      DateTime?
  classes          Class[]
  payments         Payment[]
  attendances      Attendance[]
  grades           Grade[]
  documents        Document[]
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
}

model Class {
  id           String    @id @default(cuid())
  name         String
  teacher      Teacher   @relation(fields: [teacherId], references: [id])
  teacherId    String
  subject      String
  level        String
  stage        Stage     @default(EARLY)
  language     Language  @default(RUSSIAN)
  cabinet      Cabinet   @relation(fields: [cabinetId], references: [id])
  cabinetId    String
  schedule     Json[] // Storing schedule as JSON array
  students     Student[]
  courseAmount Float
  maxStudents  Int       @default(15)
  status       String    @default("ACTIVE") // ACTIVE, COMPLETED, CANCELLED
  description  String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  openingDate  DateTime?
  endDate      DateTime?

  // New relations
  attendances Attendance[]
  grades      Grade[]
}

model Cabinet {
  id        String   @id @default(cuid())
  name      String
  capacity  Int
  equipment String[]
  status    String
  location  String
  classes   Class[]
}

model Payment {
  id            String    @id @default(cuid())
  student       Student   @relation(fields: [studentId], references: [id])
  studentId     String
  amount        Float
  date          DateTime
  status        String    @default("PENDING")
  method        String
  description   String
  invoiceNumber String?
  dueDate       DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Attendance {
  id        String   @id @default(cuid())
  student   Student  @relation(fields: [studentId], references: [id])
  studentId String
  teacher   Teacher  @relation(fields: [teacherId], references: [id])
  teacherId String
  class     Class    @relation(fields: [classId], references: [id])
  classId   String
  date      DateTime
  status    String // PRESENT, ABSENT, LATE, EXCUSED
  notes     String?
  createdAt DateTime @default(now())

  @@unique([studentId, classId, date])
  @@index([date])
  @@index([classId, date])
}

model Grade {
  id         String   @id @default(cuid())
  student    Student  @relation(fields: [studentId], references: [id])
  studentId  String
  teacher    Teacher  @relation(fields: [teacherId], references: [id])
  teacherId  String
  class      Class    @relation(fields: [classId], references: [id])
  classId    String
  subject    String
  gradeType  String // QUIZ, EXAM, HOMEWORK, PROJECT
  score      Float
  maxScore   Float
  percentage Float
  date       DateTime
  notes      String?
  createdAt  DateTime @default(now())

  @@index([studentId])
  @@index([classId])
  @@index([date])
}

model Document {
  id         String    @id @default(cuid())
  student    Student   @relation(fields: [studentId], references: [id])
  studentId  String
  name       String
  type       String // CONTRACT, CERTIFICATE, ID_COPY, etc.
  fileUrl    String
  uploadDate DateTime  @default(now())
  expiryDate DateTime?
  isVerified Boolean   @default(false)
  notes      String?
  createdAt  DateTime  @default(now())

  @@index([studentId])
  @@index([type])
}
