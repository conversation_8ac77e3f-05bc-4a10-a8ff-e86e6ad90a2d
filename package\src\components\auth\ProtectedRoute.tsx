"use client";
import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@prisma/client";
import { Box, CircularProgress } from "@mui/material";

// Define restricted routes for different roles
const restrictedRoutes = {
  [UserRole.RECEPTION]: [
    '/',  // Dashboard
    '/class-management/teachers',
    '/class-management/payments'
  ],
  [UserRole.TEACHER]: [
    '/',  // Dashboard
    '/class-management/teachers',
    '/class-management/payments',
    '/class-management/students'
  ],
  [UserRole.STUDENT]: [
    '/',  // Dashboard
    '/class-management/teachers',
    '/class-management/payments',
    '/class-management/students',
    '/class-management/classes',
    '/class-management/cabinets',
    '/class-management/timetable'
  ]
};

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isLoading, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isLoading) return; // Wait for auth check to complete

    if (!isAuthenticated && pathname !== "/authentication/login") {
      router.push("/authentication/login");
      return;
    }

    // Check if user has access to current route
    if (isAuthenticated && user) {
      const userRestrictedRoutes = restrictedRoutes[user.role] || [];
      const isRestricted = userRestrictedRoutes.some(route =>
        pathname === route || pathname.startsWith(route + '/')
      );

      if (isRestricted) {
        // Redirect to appropriate page based on role
        const redirectUrl = getRedirectUrlForRole(user.role);
        router.push(redirectUrl);
      }
    }
  }, [pathname, router, isAuthenticated, user, isLoading]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated && pathname !== "/authentication/login") {
    return null;
  }

  // Don't render restricted pages
  if (isAuthenticated && user) {
    const userRestrictedRoutes = restrictedRoutes[user.role] || [];
    const isRestricted = userRestrictedRoutes.some(route =>
      pathname === route || pathname.startsWith(route + '/')
    );

    if (isRestricted) {
      return null;
    }
  }

  return <>{children}</>;
}

function getRedirectUrlForRole(role: UserRole): string {
  switch (role) {
    case UserRole.ADMIN:
      return '/'; // Dashboard
    case UserRole.RECEPTION:
      return '/class-management/classes'; // Classes page
    case UserRole.TEACHER:
      return '/class-management/classes'; // Classes page
    case UserRole.STUDENT:
      return '/student/dashboard'; // Student dashboard (to be implemented)
    default:
      return '/authentication/login';
  }
}