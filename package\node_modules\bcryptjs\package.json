{"name": "bcryptjs", "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "version": "3.0.2", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/shaneGirish)", "<PERSON> <> (https://github.com/alexmurray)", "<PERSON> <> (https://github.com/<PERSON>)", "<PERSON> <> (https://github.com/geekymole)", "<PERSON> <<EMAIL>> (https://github.com/nisaacson)"], "repository": {"type": "url", "url": "https://github.com/dcodeIO/bcrypt.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "type": "module", "main": "umd/index.js", "types": "umd/index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "bin": {"bcrypt": "bin/bcrypt"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"build": "node scripts/build.js", "lint": "prettier --check .", "format": "prettier --write .", "test": "npm run test:unit && npm run test:typescript", "test:unit": "node tests", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json"}, "files": ["index.js", "index.d.ts", "types.d.ts", "umd/index.js", "umd/index.d.ts", "umd/types.d.ts", "umd/package.json", "LICENSE", "README.md"], "browser": {"crypto": false}, "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.1", "prettier": "^3.5.0", "typescript": "^5.7.3"}}