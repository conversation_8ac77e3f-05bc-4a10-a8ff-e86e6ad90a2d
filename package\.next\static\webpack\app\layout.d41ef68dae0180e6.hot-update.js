"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/theme/DefaultColors */ \"(app-pages-browser)/./src/utils/theme/DefaultColors.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(app-pages-browser)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction RootLayout(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                theme: _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__.baselightTheme,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQzZEO0FBQ1I7QUFDRDtBQUNFO0FBRXZDLFNBQVNJLFdBQVcsS0FJbEM7UUFKa0MsRUFDakNDLFFBQVEsRUFHVCxHQUprQztJQUtqQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNQLDREQUFhQTtnQkFBQ1EsT0FBT1Qsc0VBQWNBOztrQ0FFbEMsOERBQUNFLGlFQUFXQTs7Ozs7a0NBQ1osOERBQUNDLCtEQUFZQTtrQ0FDVkU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYjtLQWxCd0JEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyBiYXNlbGlnaHRUaGVtZSB9IGZyb20gXCJAL3V0aWxzL3RoZW1lL0RlZmF1bHRDb2xvcnNcIjtcclxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gXCJAbXVpL21hdGVyaWFsL3N0eWxlc1wiO1xyXG5pbXBvcnQgQ3NzQmFzZWxpbmUgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvQ3NzQmFzZWxpbmVcIjtcclxuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIkAvY29udGV4dHMvQXV0aENvbnRleHRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHk+XHJcbiAgICAgICAgPFRoZW1lUHJvdmlkZXIgdGhlbWU9e2Jhc2VsaWdodFRoZW1lfT5cclxuICAgICAgICAgIHsvKiBDc3NCYXNlbGluZSBraWNrc3RhcnQgYW4gZWxlZ2FudCwgY29uc2lzdGVudCwgYW5kIHNpbXBsZSBiYXNlbGluZSB0byBidWlsZCB1cG9uLiAqL31cclxuICAgICAgICAgIDxDc3NCYXNlbGluZSAvPlxyXG4gICAgICAgICAgPEF1dGhQcm92aWRlcj5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgPC9BdXRoUHJvdmlkZXI+XHJcbiAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiYmFzZWxpZ2h0VGhlbWUiLCJUaGVtZVByb3ZpZGVyIiwiQ3NzQmFzZWxpbmUiLCJBdXRoUHJvdmlkZXIiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJ0aGVtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated\n    const isAuthenticated = !!user;\n    // Role hierarchy for permission checking\n    const roleHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.STUDENT]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.TEACHER]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.RECEPTION]: 2,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN]: 3\n    };\n    // Check if user has required permission\n    const hasPermission = (requiredRole)=>{\n        if (!user) return false;\n        return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n    };\n    // Login function\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setUser(data.user);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error occurred\"\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            // Redirect to login page\n            window.location.href = \"/authentication/login\";\n        }\n    };\n    // Refresh token function\n    const refreshToken = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.user);\n                    return true;\n                }\n            }\n            // If refresh fails, logout user\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Get current user on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getCurrentUser = async ()=>{\n            try {\n                const response = await fetch(\"/api/auth/me\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    }\n                } else if (response.status === 401) {\n                    // Try to refresh token\n                    const refreshSuccess = await refreshToken();\n                    if (!refreshSuccess) {\n                        // Redirect to login if refresh fails\n                        window.location.href = \"/authentication/login\";\n                    }\n                }\n            } catch (error) {\n                console.error(\"Get current user error:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getCurrentUser();\n    }, []);\n    // Auto-refresh token before expiry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(async ()=>{\n            await refreshToken();\n        }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"l8KTfcQ0b0tPgqlKoCac6dKortU=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});