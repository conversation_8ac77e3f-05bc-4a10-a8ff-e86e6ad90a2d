import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { announcementSchema } from '@/lib/validation';
import { AuditService } from '@/lib/audit';

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const activeOnly = searchParams.get('activeOnly') !== 'false';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      OR: [
        { targetRole: null }, // Public announcements
        { targetRole: userRole } // Role-specific announcements
      ]
    };

    if (activeOnly) {
      where.isActive = true;
      where.publishAt = { lte: new Date() };
      where.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ];
    }

    const [announcements, total] = await Promise.all([
      prisma.announcement.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        },
        orderBy: [
          { isPinned: 'desc' },
          { priority: 'desc' },
          { publishAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.announcement.count({ where })
    ]);

    return NextResponse.json({
      announcements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch announcements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch announcements' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins and reception can create announcements
    if (userRole !== 'ADMIN' && userRole !== 'RECEPTION') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = announcementSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    const announcement = await prisma.announcement.create({
      data: {
        authorId: userId,
        title: data.title,
        content: data.content,
        targetRole: data.targetRole,
        priority: data.priority,
        isPinned: data.isPinned,
        attachments: data.attachments,
        publishAt: data.publishAt ? new Date(data.publishAt) : new Date(),
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    // Create notifications for target users
    const targetUsers = await prisma.user.findMany({
      where: data.targetRole ? { role: data.targetRole, isActive: true } : { isActive: true },
      select: { id: true }
    });

    const notifications = targetUsers.map(user => ({
      userId: user.id,
      type: 'ANNOUNCEMENT' as const,
      title: `New announcement: ${data.title}`,
      message: data.content.substring(0, 200) + (data.content.length > 200 ? '...' : ''),
      category: 'announcement',
      actionUrl: `/announcements/${announcement.id}`,
      priority: data.priority === 'URGENT' ? 'HIGH' as const : 'NORMAL' as const
    }));

    if (notifications.length > 0) {
      await prisma.notification.createMany({
        data: notifications
      });
    }

    // Log the action
    await AuditService.log(
      userId,
      'CREATE',
      'Announcement',
      announcement.id,
      null,
      announcement,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(announcement, { status: 201 });
  } catch (error) {
    console.error('Failed to create announcement:', error);
    return NextResponse.json(
      { error: 'Failed to create announcement' }, 
      { status: 500 }
    );
  }
}
