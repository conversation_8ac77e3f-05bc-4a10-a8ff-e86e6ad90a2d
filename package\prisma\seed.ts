import { PrismaClient, UserRole } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Starting database seeding...')

  // Clear existing data
  await prisma.auditLog.deleteMany()
  await prisma.document.deleteMany()
  await prisma.grade.deleteMany()
  await prisma.attendance.deleteMany()
  await prisma.payment.deleteMany()
  await prisma.class.deleteMany()
  await prisma.student.deleteMany()
  await prisma.teacher.deleteMany()
  await prisma.cabinet.deleteMany()
  await prisma.user.deleteMany()

  // Create admin users first
  const adminPassword = await bcrypt.hash('Admin123!', 12)
  const receptionPassword = await bcrypt.hash('Reception123!', 12)

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPassword,
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>з Администратор',
      role: UserRole.ADMIN,
      isActive: true,
    },
  })

  const receptionUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: receptionPassword,
      name: 'Ресепшн Сотрудник',
      role: UserRole.RECEPTION,
      isActive: true,
    },
  })

  console.log('Admin users created successfully!')

  // Create sample teachers
  const teacherPassword = await bcrypt.hash('Teacher123!', 12)

  const teacherUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: teacherPassword,
      name: 'Анна Иванова',
      role: UserRole.TEACHER,
      isActive: true,
    },
  })

  const teacher1 = await prisma.teacher.create({
    data: {
      userId: teacherUser1.id,
      name: 'Анна Иванова',
      email: '<EMAIL>',
      phone: '+998901234567',
      subjects: ['Математика', 'Физика'],
      qualifications: ['Магистр математики', 'Сертификат преподавателя'],
      joinDate: new Date('2023-01-15'),
      status: 'ACTIVE',
      salary: 3000000,
      address: 'Ташкент, Юнусабад',
      dateOfBirth: new Date('1985-05-15'),
    },
  })

  const teacherUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: teacherPassword,
      name: 'Дмитрий Петров',
      role: UserRole.TEACHER,
      isActive: true,
    },
  })

  const teacher2 = await prisma.teacher.create({
    data: {
      userId: teacherUser2.id,
      name: 'Дмитрий Петров',
      email: '<EMAIL>',
      phone: '+998901234568',
      subjects: ['Английский язык', 'Литература'],
      qualifications: ['Бакалавр филологии', 'IELTS сертификат'],
      joinDate: new Date('2023-02-01'),
      status: 'ACTIVE',
      salary: 2800000,
      address: 'Ташкент, Мирзо-Улугбек',
      dateOfBirth: new Date('1988-08-22'),
    },
  })

  console.log('Teachers created successfully!')

  // Create sample cabinets
  const cabinet1 = await prisma.cabinet.create({
    data: {
      name: 'Кабинет 101',
      capacity: 20,
      equipment: ['Проектор', 'Доска', 'Компьютер'],
      status: 'AVAILABLE',
      location: 'Первый этаж',
    },
  })

  const cabinet2 = await prisma.cabinet.create({
    data: {
      name: 'Кабинет 102',
      capacity: 15,
      equipment: ['Интерактивная доска', 'Планшеты'],
      status: 'AVAILABLE',
      location: 'Первый этаж',
    },
  })

  console.log('Cabinets created successfully!')

  // Create sample students
  const studentPassword = await bcrypt.hash('Student123!', 12)

  const studentUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: studentPassword,
      name: 'Алексей Смирнов',
      role: UserRole.STUDENT,
      isActive: true,
    },
  })

  const student1 = await prisma.student.create({
    data: {
      userId: studentUser1.id,
      name: 'Алексей Смирнов',
      phone: '+998901234569',
      joinDate: new Date('2023-09-01'),
      paymentStatus: 'PAID',
      status: 'ENROLLED',
      parentName: 'Иван Смирнов',
      parentPhone: '+998901234580',
      address: 'Ташкент, Чиланзар',
      dateOfBirth: new Date('2005-03-10'),
    },
  })

  const studentUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: studentPassword,
      name: 'Мария Козлова',
      role: UserRole.STUDENT,
      isActive: true,
    },
  })

  const student2 = await prisma.student.create({
    data: {
      userId: studentUser2.id,
      name: 'Мария Козлова',
      phone: '+998901234570',
      joinDate: new Date('2023-09-15'),
      paymentStatus: 'UNPAID',
      status: 'ENROLLED',
      parentName: 'Елена Козлова',
      parentPhone: '+998901234581',
      address: 'Ташкент, Сергели',
      dateOfBirth: new Date('2006-07-18'),
    },
  })

  console.log('Students created successfully!')

  // Create sample classes
  const class1 = await prisma.class.create({
    data: {
      name: 'Математика 10 класс',
      subject: 'Математика',
      level: '10 класс',
      stage: 'MIDDLE',
      language: 'RUSSIAN',
      teacherId: teacher1.id,
      cabinetId: cabinet1.id,
      courseAmount: 500000,
      maxStudents: 20,
      schedule: [
        { day: 'Понедельник', startTime: '09:00', endTime: '10:30' },
        { day: 'Среда', startTime: '09:00', endTime: '10:30' },
        { day: 'Пятница', startTime: '09:00', endTime: '10:30' },
      ],
      students: {
        connect: [{ id: student1.id }],
      },
      openingDate: new Date('2023-09-01'),
    },
  })

  const class2 = await prisma.class.create({
    data: {
      name: 'Английский язык начинающие',
      subject: 'Английский язык',
      level: 'Начинающий',
      stage: 'EARLY',
      language: 'MIXED',
      teacherId: teacher2.id,
      cabinetId: cabinet2.id,
      courseAmount: 400000,
      maxStudents: 15,
      schedule: [
        { day: 'Вторник', startTime: '14:00', endTime: '15:30' },
        { day: 'Четверг', startTime: '14:00', endTime: '15:30' },
      ],
      students: {
        connect: [{ id: student2.id }],
      },
      openingDate: new Date('2023-09-15'),
    },
  })

  console.log('Classes created successfully!')

  // Create sample payments
  await prisma.payment.create({
    data: {
      studentId: student1.id,
      amount: 500000,
      date: new Date('2023-09-01'),
      status: 'COMPLETED',
      method: 'CASH',
      description: 'Оплата за курс математики',
      invoiceNumber: 'INV-001',
    },
  })

  await prisma.payment.create({
    data: {
      studentId: student2.id,
      amount: 200000,
      date: new Date('2023-09-15'),
      status: 'PENDING',
      method: 'BANK_TRANSFER',
      description: 'Частичная оплата за курс английского',
      invoiceNumber: 'INV-002',
      dueDate: new Date('2023-10-15'),
    },
  })

  console.log('Payments created successfully!')
  console.log('Database seeded successfully!')
  console.log('Login credentials:')
  console.log('Admin: <EMAIL> / Admin123!')
  console.log('Reception: <EMAIL> / Reception123!')
  console.log('Teacher: <EMAIL> / Teacher123!')
  console.log('Student: <EMAIL> / Student123!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })