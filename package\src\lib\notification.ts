import { prisma } from './prisma';
import { NotificationType, NotificationPriority } from '@prisma/client';

export interface CreateNotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  priority?: NotificationPriority;
  category?: string;
  actionUrl?: string;
  expiresAt?: Date;
}

export class NotificationService {
  /**
   * Create a single notification
   */
  static async create(data: CreateNotificationData) {
    try {
      return await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data,
          priority: data.priority || 'NORMAL',
          category: data.category,
          actionUrl: data.actionUrl,
          expiresAt: data.expiresAt,
        },
      });
    } catch (error) {
      console.error('Failed to create notification:', error);
      throw error;
    }
  }

  /**
   * Create multiple notifications
   */
  static async createMany(notifications: CreateNotificationData[]) {
    try {
      const data = notifications.map(notification => ({
        userId: notification.userId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority || 'NORMAL' as NotificationPriority,
        category: notification.category,
        actionUrl: notification.actionUrl,
        expiresAt: notification.expiresAt,
      }));

      return await prisma.notification.createMany({ data });
    } catch (error) {
      console.error('Failed to create notifications:', error);
      throw error;
    }
  }

  /**
   * Send payment due notifications
   */
  static async sendPaymentDueNotifications() {
    try {
      // Find payments due in the next 3 days
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

      const duePayments = await prisma.payment.findMany({
        where: {
          status: 'PENDING',
          dueDate: {
            lte: threeDaysFromNow,
            gte: new Date()
          }
        },
        include: {
          student: {
            include: {
              user: true
            }
          }
        }
      });

      const notifications: CreateNotificationData[] = [];

      for (const payment of duePayments) {
        if (payment.student.user) {
          const daysUntilDue = Math.ceil(
            (payment.dueDate!.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );

          notifications.push({
            userId: payment.student.user.id,
            type: 'PAYMENT_DUE',
            title: 'Payment Due Soon',
            message: `Your payment of $${payment.amount} is due in ${daysUntilDue} day(s).`,
            data: { paymentId: payment.id, amount: payment.amount, dueDate: payment.dueDate },
            priority: daysUntilDue <= 1 ? 'HIGH' : 'NORMAL',
            category: 'payment',
            actionUrl: `/payments/${payment.id}`,
          });
        }
      }

      if (notifications.length > 0) {
        await this.createMany(notifications);
        console.log(`Sent ${notifications.length} payment due notifications`);
      }

      return notifications.length;
    } catch (error) {
      console.error('Failed to send payment due notifications:', error);
      throw error;
    }
  }

  /**
   * Send class reminder notifications
   */
  static async sendClassReminderNotifications() {
    try {
      // Find classes starting in the next 24 hours
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const upcomingClasses = await prisma.class.findMany({
        where: {
          status: 'ACTIVE',
          // Note: This is a simplified version. In a real implementation,
          // you'd need to parse the schedule JSON to find exact class times
        },
        include: {
          students: {
            include: {
              user: true
            }
          },
          teacher: {
            include: {
              user: true
            }
          }
        }
      });

      const notifications: CreateNotificationData[] = [];

      for (const classItem of upcomingClasses) {
        // Notify students
        for (const student of classItem.students) {
          if (student.user) {
            notifications.push({
              userId: student.user.id,
              type: 'CLASS_REMINDER',
              title: 'Class Reminder',
              message: `Your ${classItem.subject} class is scheduled for tomorrow.`,
              data: { classId: classItem.id, className: classItem.name },
              priority: 'NORMAL',
              category: 'class',
              actionUrl: `/classes/${classItem.id}`,
            });
          }
        }

        // Notify teacher
        if (classItem.teacher.user) {
          notifications.push({
            userId: classItem.teacher.user.id,
            type: 'CLASS_REMINDER',
            title: 'Teaching Reminder',
            message: `You have a ${classItem.subject} class scheduled for tomorrow.`,
            data: { classId: classItem.id, className: classItem.name },
            priority: 'NORMAL',
            category: 'class',
            actionUrl: `/classes/${classItem.id}`,
          });
        }
      }

      if (notifications.length > 0) {
        await this.createMany(notifications);
        console.log(`Sent ${notifications.length} class reminder notifications`);
      }

      return notifications.length;
    } catch (error) {
      console.error('Failed to send class reminder notifications:', error);
      throw error;
    }
  }

  /**
   * Send attendance alert notifications
   */
  static async sendAttendanceAlertNotifications() {
    try {
      // Find students with poor attendance (less than 80% in the last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const attendanceStats = await prisma.attendance.groupBy({
        by: ['studentId'],
        where: {
          date: {
            gte: thirtyDaysAgo
          }
        },
        _count: {
          _all: true
        },
        _sum: {
          status: true // This would need to be calculated differently in practice
        }
      });

      const notifications: CreateNotificationData[] = [];

      // This is a simplified version - in practice, you'd calculate attendance percentages
      // and send notifications to students with poor attendance

      if (notifications.length > 0) {
        await this.createMany(notifications);
        console.log(`Sent ${notifications.length} attendance alert notifications`);
      }

      return notifications.length;
    } catch (error) {
      console.error('Failed to send attendance alert notifications:', error);
      throw error;
    }
  }

  /**
   * Clean up expired notifications
   */
  static async cleanupExpiredNotifications() {
    try {
      const result = await prisma.notification.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      console.log(`Cleaned up ${result.count} expired notifications`);
      return result.count;
    } catch (error) {
      console.error('Failed to cleanup expired notifications:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics for a user
   */
  static async getNotificationStats(userId: string) {
    try {
      const [total, unread, byType] = await Promise.all([
        prisma.notification.count({
          where: { userId }
        }),
        prisma.notification.count({
          where: { 
            userId,
            isRead: false,
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          }
        }),
        prisma.notification.groupBy({
          by: ['type'],
          where: { 
            userId,
            isRead: false,
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          },
          _count: {
            _all: true
          }
        })
      ]);

      return {
        total,
        unread,
        byType: byType.reduce((acc, item) => {
          acc[item.type] = item._count._all;
          return acc;
        }, {} as Record<string, number>)
      };
    } catch (error) {
      console.error('Failed to get notification stats:', error);
      throw error;
    }
  }
}
