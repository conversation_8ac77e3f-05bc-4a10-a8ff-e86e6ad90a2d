import { NextRequest, NextResponse } from 'next/server';
import { AutomationService } from '@/lib/automation';
import { AuditService } from '@/lib/audit';
import { z } from 'zod';

const triggerSchema = z.object({
  trigger: z.enum([
    'PAYMENT_DUE',
    'PAYMENT_OVERDUE',
    'CLASS_STARTING',
    'STUDENT_ABSENT',
    'GRADE_BELOW_THRESHOLD',
    'ENROLLMENT_EXPIRING',
    'DOCUMENT_EXPIRING',
    'SCHEDULED_TIME'
  ]),
  context: z.record(z.any()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can manually trigger automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = triggerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { trigger, context = {} } = validationResult.data;

    // Add metadata to context
    const enrichedContext = {
      ...context,
      triggeredBy: userId,
      triggeredAt: new Date(),
      manual: true,
    };

    // Execute the trigger
    const executedCount = await AutomationService.executeTrigger(trigger, enrichedContext);

    // Log the manual trigger
    await AuditService.log(
      userId,
      'TRIGGER',
      'AutomationRule',
      'manual_trigger',
      null,
      { trigger, context: enrichedContext, executedCount },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({
      success: true,
      trigger,
      executedCount,
      message: `Executed ${executedCount} automation rule(s) for trigger: ${trigger}`
    });
  } catch (error) {
    console.error('Failed to trigger automation rules:', error);
    return NextResponse.json(
      { error: 'Failed to trigger automation rules' }, 
      { status: 500 }
    );
  }
}
