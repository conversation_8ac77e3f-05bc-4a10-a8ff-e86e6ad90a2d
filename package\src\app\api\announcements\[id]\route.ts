import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { announcementSchema } from '@/lib/validation';
import { AuditService } from '@/lib/audit';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const announcement = await prisma.announcement.findFirst({
      where: {
        id: params.id,
        OR: [
          { targetRole: null }, // Public announcements
          { targetRole: userRole } // Role-specific announcements
        ]
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!announcement) {
      return NextResponse.json(
        { error: 'Announcement not found' }, 
        { status: 404 }
      );
    }

    return NextResponse.json(announcement);
  } catch (error) {
    console.error('Failed to fetch announcement:', error);
    return NextResponse.json(
      { error: 'Failed to fetch announcement' }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins and reception can edit announcements
    if (userRole !== 'ADMIN' && userRole !== 'RECEPTION') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = announcementSchema.partial().safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Find the announcement first
    const existingAnnouncement = await prisma.announcement.findUnique({
      where: { id: params.id }
    });

    if (!existingAnnouncement) {
      return NextResponse.json(
        { error: 'Announcement not found' }, 
        { status: 404 }
      );
    }

    // Only author or admin can edit
    if (existingAnnouncement.authorId !== userId && userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Access denied' }, 
        { status: 403 }
      );
    }

    const updateData: any = {};
    
    if (data.title !== undefined) updateData.title = data.title;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.targetRole !== undefined) updateData.targetRole = data.targetRole;
    if (data.priority !== undefined) updateData.priority = data.priority;
    if (data.isPinned !== undefined) updateData.isPinned = data.isPinned;
    if (data.attachments !== undefined) updateData.attachments = data.attachments;
    if (data.publishAt !== undefined) updateData.publishAt = new Date(data.publishAt);
    if (data.expiresAt !== undefined) updateData.expiresAt = data.expiresAt ? new Date(data.expiresAt) : null;

    const updatedAnnouncement = await prisma.announcement.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    // Log the action
    await AuditService.log(
      userId,
      'UPDATE',
      'Announcement',
      params.id,
      existingAnnouncement,
      updatedAnnouncement,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(updatedAnnouncement);
  } catch (error) {
    console.error('Failed to update announcement:', error);
    return NextResponse.json(
      { error: 'Failed to update announcement' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins and reception can delete announcements
    if (userRole !== 'ADMIN' && userRole !== 'RECEPTION') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    // Find the announcement first
    const existingAnnouncement = await prisma.announcement.findUnique({
      where: { id: params.id }
    });

    if (!existingAnnouncement) {
      return NextResponse.json(
        { error: 'Announcement not found' }, 
        { status: 404 }
      );
    }

    // Only author or admin can delete
    if (existingAnnouncement.authorId !== userId && userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Access denied' }, 
        { status: 403 }
      );
    }

    await prisma.announcement.delete({
      where: { id: params.id }
    });

    // Log the action
    await AuditService.log(
      userId,
      'DELETE',
      'Announcement',
      params.id,
      existingAnnouncement,
      null,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete announcement:', error);
    return NextResponse.json(
      { error: 'Failed to delete announcement' }, 
      { status: 500 }
    );
  }
}
