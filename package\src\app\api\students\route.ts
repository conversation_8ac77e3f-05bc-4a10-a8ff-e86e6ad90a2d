import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { studentSchema } from '@/lib/validation';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only Admin and Reception can view all students
    if (!AuthService.hasPermission(userRole, UserRole.RECEPTION)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const students = await prisma.student.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            isActive: true,
          },
        },
        classes: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
        payments: {
          select: {
            id: true,
            amount: true,
            date: true,
            status: true,
          },
          orderBy: {
            date: 'desc',
          },
          take: 5, // Last 5 payments
        },
        _count: {
          select: {
            attendances: true,
            grades: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'READ',
      'Student',
      'all',
      null,
      null,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(students);
  } catch (error) {
    console.error('Get students error:', error);
    return NextResponse.json({ error: 'Failed to fetch students' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only Admin and Reception can create students
    if (!AuthService.hasPermission(userRole, UserRole.RECEPTION)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();

    // Validate input
    const validationResult = studentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Create user account if email is provided
    let userAccount = null;
    if (data.email) {
      // Generate a temporary password
      const tempPassword = Math.random().toString(36).slice(-8) + 'A1!';
      const hashedPassword = await AuthService.hashPassword(tempPassword);

      userAccount = await prisma.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          name: data.name,
          role: UserRole.STUDENT,
          isActive: true,
        },
      });
    }

    const student = await prisma.student.create({
      data: {
        userId: userAccount?.id,
        name: data.name,
        phone: data.phone,
        joinDate: new Date(data.joinDate),
        paymentStatus: data.paymentStatus || 'UNPAID',
        status: data.status || 'INQUIRY',
        parentName: data.parentName,
        parentPhone: data.parentPhone,
        emergencyContact: data.emergencyContact,
        address: data.address,
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,
        ...(data.classIds && {
          classes: {
            connect: data.classIds.map((id: string) => ({ id })),
          },
        }),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            isActive: true,
          },
        },
        classes: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'CREATE',
      'Student',
      student.id,
      null,
      student,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(student);
  } catch (error) {
    console.error('Create student error:', error);
    return NextResponse.json({ error: 'Failed to create student' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only Admin and Reception can update students
    if (!AuthService.hasPermission(userRole, UserRole.RECEPTION)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json({ error: 'Student ID is required' }, { status: 400 });
    }

    // Validate input
    const validationResult = studentSchema.partial().safeParse(updateData);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Get current student data for audit log
    const currentStudent = await prisma.student.findUnique({
      where: { id },
      include: {
        user: true,
        classes: true,
      },
    });

    if (!currentStudent) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 });
    }

    const student = await prisma.student.update({
      where: { id },
      data: {
        name: data.name,
        phone: data.phone,
        joinDate: data.joinDate ? new Date(data.joinDate) : undefined,
        paymentStatus: data.paymentStatus,
        status: data.status,
        parentName: data.parentName,
        parentPhone: data.parentPhone,
        emergencyContact: data.emergencyContact,
        address: data.address,
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : undefined,
        ...(data.classIds && {
          classes: {
            set: data.classIds.map((id: string) => ({ id })),
          },
        }),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            isActive: true,
          },
        },
        classes: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'UPDATE',
      'Student',
      student.id,
      currentStudent,
      student,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(student);
  } catch (error) {
    console.error('Update student error:', error);
    return NextResponse.json({ error: 'Failed to update student' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only Admin can delete students
    if (!AuthService.hasPermission(userRole, UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Student ID is required' }, { status: 400 });
    }

    // Get current student data for audit log
    const currentStudent = await prisma.student.findUnique({
      where: { id },
      include: {
        user: true,
        classes: true,
        payments: true,
      },
    });

    if (!currentStudent) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 });
    }

    // Delete related user account if exists
    if (currentStudent.userId) {
      await prisma.user.delete({
        where: { id: currentStudent.userId },
      });
    }

    // Delete student (cascade will handle related records)
    await prisma.student.delete({
      where: { id },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'DELETE',
      'Student',
      id,
      currentStudent,
      null,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ message: 'Student deleted successfully' });
  } catch (error) {
    console.error('Delete student error:', error);
    return NextResponse.json({ error: 'Failed to delete student' }, { status: 500 });
  }
}