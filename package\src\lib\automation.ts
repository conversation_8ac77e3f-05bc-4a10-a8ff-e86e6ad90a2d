import { prisma } from './prisma';
import { NotificationService } from './notification';
import { AutomationTrigger } from '@prisma/client';

export interface AutomationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in';
  value: any;
}

export interface AutomationAction {
  type: 'send_notification' | 'send_email' | 'update_status' | 'create_task';
  parameters: any;
}

export interface AutomationRuleData {
  name: string;
  description?: string;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  isActive?: boolean;
}

export class AutomationService {
  /**
   * Create a new automation rule
   */
  static async createRule(createdBy: string, data: AutomationRuleData) {
    try {
      return await prisma.automationRule.create({
        data: {
          name: data.name,
          description: data.description,
          trigger: data.trigger,
          conditions: data.conditions,
          actions: data.actions,
          isActive: data.isActive ?? true,
          createdBy,
        },
      });
    } catch (error) {
      console.error('Failed to create automation rule:', error);
      throw error;
    }
  }

  /**
   * Execute automation rules for a specific trigger
   */
  static async executeTrigger(trigger: AutomationTrigger, context: any) {
    try {
      const rules = await prisma.automationRule.findMany({
        where: {
          trigger,
          isActive: true,
        },
      });

      let executedCount = 0;

      for (const rule of rules) {
        try {
          const shouldExecute = this.evaluateConditions(
            rule.conditions as AutomationCondition[],
            context
          );

          if (shouldExecute) {
            await this.executeActions(
              rule.actions as AutomationAction[],
              context
            );

            // Update rule statistics
            await prisma.automationRule.update({
              where: { id: rule.id },
              data: {
                lastRun: new Date(),
                runCount: { increment: 1 },
              },
            });

            // Log successful execution
            await prisma.automationLog.create({
              data: {
                ruleId: rule.id,
                status: 'SUCCESS',
                message: `Rule executed successfully`,
                data: context,
              },
            });

            executedCount++;
          } else {
            // Log skipped execution
            await prisma.automationLog.create({
              data: {
                ruleId: rule.id,
                status: 'SKIPPED',
                message: `Conditions not met`,
                data: context,
              },
            });
          }
        } catch (error) {
          console.error(`Failed to execute automation rule ${rule.id}:`, error);

          // Log failed execution
          await prisma.automationLog.create({
            data: {
              ruleId: rule.id,
              status: 'FAILED',
              message: `Execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              data: context,
            },
          });
        }
      }

      return executedCount;
    } catch (error) {
      console.error('Failed to execute automation trigger:', error);
      throw error;
    }
  }

  /**
   * Evaluate automation conditions
   */
  private static evaluateConditions(
    conditions: AutomationCondition[],
    context: any
  ): boolean {
    if (!conditions || conditions.length === 0) {
      return true;
    }

    return conditions.every(condition => {
      const contextValue = this.getNestedValue(context, condition.field);
      
      switch (condition.operator) {
        case 'equals':
          return contextValue === condition.value;
        case 'not_equals':
          return contextValue !== condition.value;
        case 'greater_than':
          return Number(contextValue) > Number(condition.value);
        case 'less_than':
          return Number(contextValue) < Number(condition.value);
        case 'contains':
          return String(contextValue).toLowerCase().includes(String(condition.value).toLowerCase());
        case 'in':
          return Array.isArray(condition.value) && condition.value.includes(contextValue);
        default:
          return false;
      }
    });
  }

  /**
   * Execute automation actions
   */
  private static async executeActions(
    actions: AutomationAction[],
    context: any
  ) {
    for (const action of actions) {
      switch (action.type) {
        case 'send_notification':
          await this.executeSendNotificationAction(action.parameters, context);
          break;
        case 'send_email':
          await this.executeSendEmailAction(action.parameters, context);
          break;
        case 'update_status':
          await this.executeUpdateStatusAction(action.parameters, context);
          break;
        case 'create_task':
          await this.executeCreateTaskAction(action.parameters, context);
          break;
        default:
          console.warn(`Unknown automation action type: ${action.type}`);
      }
    }
  }

  /**
   * Execute send notification action
   */
  private static async executeSendNotificationAction(
    parameters: any,
    context: any
  ) {
    const { userId, type, title, message, priority, category, actionUrl } = parameters;
    
    // Replace placeholders in title and message
    const processedTitle = this.replacePlaceholders(title, context);
    const processedMessage = this.replacePlaceholders(message, context);
    
    await NotificationService.create({
      userId: userId || context.userId,
      type: type || 'SYSTEM_ALERT',
      title: processedTitle,
      message: processedMessage,
      priority: priority || 'NORMAL',
      category: category || 'automation',
      actionUrl: actionUrl ? this.replacePlaceholders(actionUrl, context) : undefined,
      data: context,
    });
  }

  /**
   * Execute send email action (placeholder for future email integration)
   */
  private static async executeSendEmailAction(
    parameters: any,
    context: any
  ) {
    // TODO: Implement email sending functionality when email templates are ready
    console.log('Email action executed:', parameters, context);
  }

  /**
   * Execute update status action
   */
  private static async executeUpdateStatusAction(
    parameters: any,
    context: any
  ) {
    const { entity, entityId, status } = parameters;
    
    switch (entity) {
      case 'student':
        await prisma.student.update({
          where: { id: entityId || context.studentId },
          data: { status },
        });
        break;
      case 'payment':
        await prisma.payment.update({
          where: { id: entityId || context.paymentId },
          data: { status },
        });
        break;
      // Add more entities as needed
    }
  }

  /**
   * Execute create task action (placeholder for future task management)
   */
  private static async executeCreateTaskAction(
    parameters: any,
    context: any
  ) {
    // TODO: Implement task creation functionality
    console.log('Task creation action executed:', parameters, context);
  }

  /**
   * Replace placeholders in text with context values
   */
  private static replacePlaceholders(text: string, context: any): string {
    return text.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(context, path);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Get nested value from object using dot notation
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Run scheduled automation tasks
   */
  static async runScheduledTasks() {
    try {
      console.log('Running scheduled automation tasks...');

      // Execute payment due notifications
      await NotificationService.sendPaymentDueNotifications();

      // Execute class reminder notifications
      await NotificationService.sendClassReminderNotifications();

      // Execute attendance alert notifications
      await NotificationService.sendAttendanceAlertNotifications();

      // Clean up expired notifications
      await NotificationService.cleanupExpiredNotifications();

      // Execute scheduled automation rules
      await this.executeTrigger('SCHEDULED_TIME', {
        timestamp: new Date(),
        type: 'scheduled_run',
      });

      console.log('Scheduled automation tasks completed');
    } catch (error) {
      console.error('Failed to run scheduled automation tasks:', error);
      throw error;
    }
  }

  /**
   * Get automation rule statistics
   */
  static async getRuleStats(ruleId: string) {
    try {
      const [rule, logs] = await Promise.all([
        prisma.automationRule.findUnique({
          where: { id: ruleId },
        }),
        prisma.automationLog.groupBy({
          by: ['status'],
          where: { ruleId },
          _count: {
            _all: true,
          },
        }),
      ]);

      if (!rule) {
        throw new Error('Automation rule not found');
      }

      const stats = logs.reduce((acc, log) => {
        acc[log.status] = log._count._all;
        return acc;
      }, {} as Record<string, number>);

      return {
        rule,
        stats: {
          total: rule.runCount,
          success: stats.SUCCESS || 0,
          failed: stats.FAILED || 0,
          skipped: stats.SKIPPED || 0,
        },
      };
    } catch (error) {
      console.error('Failed to get automation rule stats:', error);
      throw error;
    }
  }
}
