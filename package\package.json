{"name": "modernize-next-free", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "seed": "npx tsx prisma/seed.ts"}, "dependencies": {"@emotion/cache": "11.11.0", "@emotion/react": "11.11.1", "@emotion/server": "11.11.0", "@emotion/styled": "11.11.0", "@mui/icons-material": "5.15.15", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "5.15.15", "@prisma/client": "^5.10.2", "@tabler/icons-react": "2.30.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "20.4.5", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "apexcharts": "^3.41.1", "bcryptjs": "^3.0.2", "eslint": "8.46.0", "eslint-config-next": "13.4.12", "jsonwebtoken": "^9.0.2", "lodash": "4.17.21", "next": "14.2.3", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "18.2.0", "react-helmet-async": "1.3.0", "react-mui-sidebar": "^1.3.8", "tsx": "^4.19.4", "typescript": "5.1.6", "zod": "^3.25.49"}, "devDependencies": {"@types/lodash": "4.14.196", "prisma": "^5.10.2", "ts-node": "^10.9.2"}}