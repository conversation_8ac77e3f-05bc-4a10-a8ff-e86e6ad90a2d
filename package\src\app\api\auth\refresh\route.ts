import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Get refresh token from cookies or body
    const refreshToken = request.cookies.get('refreshToken')?.value;
    
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token not found' }, 
        { status: 401 }
      );
    }

    // Attempt token refresh
    const result = await AuthService.refreshToken(refreshToken);

    if (!result.success) {
      // Clear invalid cookies
      const response = NextResponse.json(
        { error: result.error }, 
        { status: 401 }
      );
      
      response.cookies.delete('accessToken');
      response.cookies.delete('refreshToken');
      
      return response;
    }

    // Create response with new tokens
    const response = NextResponse.json({
      success: true,
      user: result.user,
      accessToken: result.accessToken,
    });

    // Set new secure HTTP-only cookies
    response.cookies.set('accessToken', result.accessToken!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60, // 15 minutes
      path: '/',
    });

    response.cookies.set('refreshToken', result.refreshToken!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Refresh token API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
