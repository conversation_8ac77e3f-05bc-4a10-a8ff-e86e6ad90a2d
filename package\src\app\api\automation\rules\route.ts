import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { automationRuleSchema } from '@/lib/validation';
import { AutomationService } from '@/lib/automation';
import { AuditService } from '@/lib/audit';

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can view automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const trigger = searchParams.get('trigger');
    const activeOnly = searchParams.get('activeOnly') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (trigger) {
      where.trigger = trigger;
    }
    
    if (activeOnly) {
      where.isActive = true;
    }

    const [rules, total] = await Promise.all([
      prisma.automationRule.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          _count: {
            select: {
              logs: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit,
      }),
      prisma.automationRule.count({ where })
    ]);

    return NextResponse.json({
      rules,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch automation rules:', error);
    return NextResponse.json(
      { error: 'Failed to fetch automation rules' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can create automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = automationRuleSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    const rule = await AutomationService.createRule(userId, {
      name: data.name,
      description: data.description,
      trigger: data.trigger,
      conditions: data.conditions,
      actions: data.actions,
      isActive: data.isActive,
    });

    // Log the action
    await AuditService.log(
      userId,
      'CREATE',
      'AutomationRule',
      rule.id,
      null,
      rule,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(rule, { status: 201 });
  } catch (error) {
    console.error('Failed to create automation rule:', error);
    return NextResponse.json(
      { error: 'Failed to create automation rule' }, 
      { status: 500 }
    );
  }
}
