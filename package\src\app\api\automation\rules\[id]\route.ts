import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { automationRuleSchema } from '@/lib/validation';
import { AutomationService } from '@/lib/automation';
import { AuditService } from '@/lib/audit';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can view automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const rule = await prisma.automationRule.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        logs: {
          orderBy: {
            executedAt: 'desc'
          },
          take: 10 // Last 10 execution logs
        }
      }
    });

    if (!rule) {
      return NextResponse.json(
        { error: 'Automation rule not found' }, 
        { status: 404 }
      );
    }

    // Get rule statistics
    const stats = await AutomationService.getRuleStats(params.id);

    return NextResponse.json({
      ...rule,
      stats: stats.stats
    });
  } catch (error) {
    console.error('Failed to fetch automation rule:', error);
    return NextResponse.json(
      { error: 'Failed to fetch automation rule' }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can edit automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = automationRuleSchema.partial().safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Find the rule first
    const existingRule = await prisma.automationRule.findUnique({
      where: { id: params.id }
    });

    if (!existingRule) {
      return NextResponse.json(
        { error: 'Automation rule not found' }, 
        { status: 404 }
      );
    }

    const updateData: any = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.trigger !== undefined) updateData.trigger = data.trigger;
    if (data.conditions !== undefined) updateData.conditions = data.conditions;
    if (data.actions !== undefined) updateData.actions = data.actions;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    const updatedRule = await prisma.automationRule.update({
      where: { id: params.id },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    // Log the action
    await AuditService.log(
      userId,
      'UPDATE',
      'AutomationRule',
      params.id,
      existingRule,
      updatedRule,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(updatedRule);
  } catch (error) {
    console.error('Failed to update automation rule:', error);
    return NextResponse.json(
      { error: 'Failed to update automation rule' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can delete automation rules
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    // Find the rule first
    const existingRule = await prisma.automationRule.findUnique({
      where: { id: params.id }
    });

    if (!existingRule) {
      return NextResponse.json(
        { error: 'Automation rule not found' }, 
        { status: 404 }
      );
    }

    // Delete the rule and its logs
    await prisma.automationLog.deleteMany({
      where: { ruleId: params.id }
    });

    await prisma.automationRule.delete({
      where: { id: params.id }
    });

    // Log the action
    await AuditService.log(
      userId,
      'DELETE',
      'AutomationRule',
      params.id,
      existingRule,
      null,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete automation rule:', error);
    return NextResponse.json(
      { error: 'Failed to delete automation rule' }, 
      { status: 500 }
    );
  }
}
