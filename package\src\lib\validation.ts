import { z } from 'zod';

// Authentication schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['ADMIN', 'RECEPTION', 'TEACHER', 'STUDENT']).default('STUDENT'),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Student schemas
export const studentSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  email: z.string().email('Invalid email address').optional(),
  joinDate: z.string().or(z.date()),
  paymentStatus: z.enum(['PAID', 'UNPAID', 'PARTIAL']).default('UNPAID'),
  status: z.enum(['INQUIRY', 'LEAD', 'ENROLLED', 'GRADUATED', 'DROPPED', 'SUSPENDED']).default('INQUIRY'),
  parentName: z.string().optional(),
  parentPhone: z.string().optional(),
  emergencyContact: z.string().optional(),
  address: z.string().optional(),
  dateOfBirth: z.string().or(z.date()).optional(),
  classIds: z.array(z.string()).optional(),
});

// Teacher schemas
export const teacherSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  subjects: z.array(z.string()).min(1, 'At least one subject is required'),
  qualifications: z.array(z.string()).min(1, 'At least one qualification is required'),
  joinDate: z.string().or(z.date()),
  status: z.enum(['ACTIVE', 'INACTIVE', 'ON_LEAVE']).default('ACTIVE'),
  salary: z.number().positive().optional(),
  address: z.string().optional(),
  dateOfBirth: z.string().or(z.date()).optional(),
  emergencyContact: z.string().optional(),
});

// Class schemas
export const classSchema = z.object({
  name: z.string().min(2, 'Class name must be at least 2 characters'),
  subject: z.string().min(2, 'Subject must be at least 2 characters'),
  level: z.string().min(1, 'Level is required'),
  stage: z.enum(['EARLY', 'MIDDLE', 'LATE']).default('EARLY'),
  language: z.enum(['RUSSIAN', 'UZBEK', 'MIXED']).default('RUSSIAN'),
  teacherId: z.string().min(1, 'Teacher is required'),
  cabinetId: z.string().min(1, 'Cabinet is required'),
  courseAmount: z.number().positive('Course amount must be positive'),
  maxStudents: z.number().int().positive().default(15),
  description: z.string().optional(),
  openingDate: z.string().or(z.date()).optional(),
  endDate: z.string().or(z.date()).optional(),
  schedule: z.array(z.object({
    day: z.string(),
    startTime: z.string(),
    endTime: z.string(),
  })),
  studentIds: z.array(z.string()).optional(),
});

// Payment schemas
export const paymentSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  amount: z.number().positive('Amount must be positive'),
  date: z.string().or(z.date()),
  status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED']).default('PENDING'),
  method: z.enum(['CASH', 'CARD', 'BANK_TRANSFER', 'ONLINE']),
  description: z.string().min(1, 'Description is required'),
  invoiceNumber: z.string().optional(),
  dueDate: z.string().or(z.date()).optional(),
});

// Cabinet schemas
export const cabinetSchema = z.object({
  name: z.string().min(1, 'Cabinet name is required'),
  capacity: z.number().int().positive('Capacity must be positive'),
  equipment: z.array(z.string()).default([]),
  status: z.enum(['AVAILABLE', 'OCCUPIED', 'MAINTENANCE']).default('AVAILABLE'),
  location: z.string().min(1, 'Location is required'),
});

// Attendance schemas
export const attendanceSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  teacherId: z.string().min(1, 'Teacher is required'),
  classId: z.string().min(1, 'Class is required'),
  date: z.string().or(z.date()),
  status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']),
  notes: z.string().optional(),
});

// Grade schemas
export const gradeSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  teacherId: z.string().min(1, 'Teacher is required'),
  classId: z.string().min(1, 'Class is required'),
  subject: z.string().min(1, 'Subject is required'),
  gradeType: z.enum(['QUIZ', 'EXAM', 'HOMEWORK', 'PROJECT']),
  score: z.number().min(0, 'Score cannot be negative'),
  maxScore: z.number().positive('Max score must be positive'),
  date: z.string().or(z.date()),
  notes: z.string().optional(),
}).refine((data) => data.score <= data.maxScore, {
  message: "Score cannot be greater than max score",
  path: ["score"],
});

// Export types
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type StudentInput = z.infer<typeof studentSchema>;
export type TeacherInput = z.infer<typeof teacherSchema>;
export type ClassInput = z.infer<typeof classSchema>;
export type PaymentInput = z.infer<typeof paymentSchema>;
export type CabinetInput = z.infer<typeof cabinetSchema>;
export type AttendanceInput = z.infer<typeof attendanceSchema>;
export type GradeInput = z.infer<typeof gradeSchema>;
