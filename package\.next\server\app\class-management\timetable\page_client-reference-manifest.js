globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/class-management/timetable/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/authentication/login/page.tsx":{"*":{"id":"(ssr)/./src/app/authentication/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/page.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/class-management/timetable/page.tsx":{"*":{"id":"(ssr)/./src/app/class-management/timetable/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/class-management/layout.tsx":{"*":{"id":"(ssr)/./src/app/class-management/layout.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\authentication\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/authentication/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\(DashboardLayout)\\page.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/page.tsx","name":"*","chunks":["app/(DashboardLayout)/page","static/chunks/app/(DashboardLayout)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\(DashboardLayout)\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx","name":"*","chunks":["app/(DashboardLayout)/layout","static/chunks/app/(DashboardLayout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\class-management\\timetable\\page.tsx":{"id":"(app-pages-browser)/./src/app/class-management/timetable/page.tsx","name":"*","chunks":["app/class-management/timetable/page","static/chunks/app/class-management/timetable/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\class-management\\layout.tsx":{"id":"(app-pages-browser)/./src/app/class-management/layout.tsx","name":"*","chunks":["app/class-management/layout","static/chunks/app/class-management/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\loading":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\(DashboardLayout)\\page":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\(DashboardLayout)\\layout":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\(DashboardLayout)\\loading":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\class-management\\layout":[],"C:\\Users\\<USER>\\Desktop\\codes\\Improved-CRM\\package\\src\\app\\class-management\\timetable\\page":[]}}