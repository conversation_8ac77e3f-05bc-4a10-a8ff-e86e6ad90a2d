/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/authentication/login/page",{

/***/ "(app-pages-browser)/./node_modules/.prisma/client/index-browser.js":
/*!******************************************************!*\
  !*** ./node_modules/.prisma/client/index-browser.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nconst {\n  Decimal,\n  objectEnumValues,\n  makeStrictEnum,\n  Public,\n  getRuntime,\n  skip\n} = __webpack_require__(/*! @prisma/client/runtime/index-browser.js */ \"(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js\")\n\n\nconst Prisma = {}\n\nexports.Prisma = Prisma\nexports.$Enums = {}\n\n/**\n * Prisma Client JS version: 5.22.0\n * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2\n */\nPrisma.prismaVersion = {\n  client: \"5.22.0\",\n  engine: \"605197351a3c8bdd595af2d2a9bc3025bca48ea2\"\n}\n\nPrisma.PrismaClientKnownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)};\nPrisma.PrismaClientUnknownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientRustPanicError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientInitializationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientValidationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.NotFoundError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.Decimal = Decimal\n\n/**\n * Re-export of sql-template-tag\n */\nPrisma.sql = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.empty = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.join = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.raw = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.validator = Public.validator\n\n/**\n* Extensions\n*/\nPrisma.getExtensionContext = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.defineExtension = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\n\n/**\n * Shorthand utilities for JSON filtering\n */\nPrisma.DbNull = objectEnumValues.instances.DbNull\nPrisma.JsonNull = objectEnumValues.instances.JsonNull\nPrisma.AnyNull = objectEnumValues.instances.AnyNull\n\nPrisma.NullTypes = {\n  DbNull: objectEnumValues.classes.DbNull,\n  JsonNull: objectEnumValues.classes.JsonNull,\n  AnyNull: objectEnumValues.classes.AnyNull\n}\n\n\n\n/**\n * Enums\n */\n\nexports.Prisma.TransactionIsolationLevel = makeStrictEnum({\n  ReadUncommitted: 'ReadUncommitted',\n  ReadCommitted: 'ReadCommitted',\n  RepeatableRead: 'RepeatableRead',\n  Serializable: 'Serializable'\n});\n\nexports.Prisma.UserScalarFieldEnum = {\n  id: 'id',\n  email: 'email',\n  password: 'password',\n  role: 'role',\n  name: 'name',\n  isActive: 'isActive',\n  lastLoginAt: 'lastLoginAt',\n  failedAttempts: 'failedAttempts',\n  lastFailedAttempt: 'lastFailedAttempt',\n  isLocked: 'isLocked',\n  twoFactorEnabled: 'twoFactorEnabled',\n  twoFactorSecret: 'twoFactorSecret',\n  refreshToken: 'refreshToken',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.AuditLogScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  action: 'action',\n  entity: 'entity',\n  entityId: 'entityId',\n  oldData: 'oldData',\n  newData: 'newData',\n  ipAddress: 'ipAddress',\n  userAgent: 'userAgent',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.TeacherScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  name: 'name',\n  email: 'email',\n  phone: 'phone',\n  subjects: 'subjects',\n  qualifications: 'qualifications',\n  joinDate: 'joinDate',\n  status: 'status',\n  salary: 'salary',\n  address: 'address',\n  dateOfBirth: 'dateOfBirth',\n  photoUrl: 'photoUrl',\n  emergencyContact: 'emergencyContact',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.StudentScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  name: 'name',\n  phone: 'phone',\n  joinDate: 'joinDate',\n  paymentStatus: 'paymentStatus',\n  status: 'status',\n  parentName: 'parentName',\n  parentPhone: 'parentPhone',\n  emergencyContact: 'emergencyContact',\n  photoUrl: 'photoUrl',\n  address: 'address',\n  dateOfBirth: 'dateOfBirth',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.ClassScalarFieldEnum = {\n  id: 'id',\n  name: 'name',\n  teacherId: 'teacherId',\n  subject: 'subject',\n  level: 'level',\n  stage: 'stage',\n  language: 'language',\n  cabinetId: 'cabinetId',\n  schedule: 'schedule',\n  courseAmount: 'courseAmount',\n  maxStudents: 'maxStudents',\n  status: 'status',\n  description: 'description',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  openingDate: 'openingDate',\n  endDate: 'endDate'\n};\n\nexports.Prisma.CabinetScalarFieldEnum = {\n  id: 'id',\n  name: 'name',\n  capacity: 'capacity',\n  equipment: 'equipment',\n  status: 'status',\n  location: 'location'\n};\n\nexports.Prisma.PaymentScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  amount: 'amount',\n  date: 'date',\n  status: 'status',\n  method: 'method',\n  description: 'description',\n  invoiceNumber: 'invoiceNumber',\n  dueDate: 'dueDate',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.AttendanceScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  teacherId: 'teacherId',\n  classId: 'classId',\n  date: 'date',\n  status: 'status',\n  notes: 'notes',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.GradeScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  teacherId: 'teacherId',\n  classId: 'classId',\n  subject: 'subject',\n  gradeType: 'gradeType',\n  score: 'score',\n  maxScore: 'maxScore',\n  percentage: 'percentage',\n  date: 'date',\n  notes: 'notes',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.DocumentScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  name: 'name',\n  type: 'type',\n  fileUrl: 'fileUrl',\n  uploadDate: 'uploadDate',\n  expiryDate: 'expiryDate',\n  isVerified: 'isVerified',\n  notes: 'notes',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.SortOrder = {\n  asc: 'asc',\n  desc: 'desc'\n};\n\nexports.Prisma.NullableJsonNullValueInput = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull\n};\n\nexports.Prisma.QueryMode = {\n  default: 'default',\n  insensitive: 'insensitive'\n};\n\nexports.Prisma.NullsOrder = {\n  first: 'first',\n  last: 'last'\n};\n\nexports.Prisma.JsonNullValueFilter = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull,\n  AnyNull: Prisma.AnyNull\n};\nexports.UserRole = exports.$Enums.UserRole = {\n  ADMIN: 'ADMIN',\n  RECEPTION: 'RECEPTION',\n  TEACHER: 'TEACHER',\n  STUDENT: 'STUDENT'\n};\n\nexports.StudentStatus = exports.$Enums.StudentStatus = {\n  INQUIRY: 'INQUIRY',\n  LEAD: 'LEAD',\n  ENROLLED: 'ENROLLED',\n  GRADUATED: 'GRADUATED',\n  DROPPED: 'DROPPED',\n  SUSPENDED: 'SUSPENDED'\n};\n\nexports.Stage = exports.$Enums.Stage = {\n  EARLY: 'EARLY',\n  MIDDLE: 'MIDDLE',\n  LATE: 'LATE'\n};\n\nexports.Language = exports.$Enums.Language = {\n  RUSSIAN: 'RUSSIAN',\n  UZBEK: 'UZBEK',\n  MIXED: 'MIXED'\n};\n\nexports.Prisma.ModelName = {\n  User: 'User',\n  AuditLog: 'AuditLog',\n  Teacher: 'Teacher',\n  Student: 'Student',\n  Class: 'Class',\n  Cabinet: 'Cabinet',\n  Payment: 'Payment',\n  Attendance: 'Attendance',\n  Grade: 'Grade',\n  Document: 'Document'\n};\n\n/**\n * This is a stub Prisma Client that will error at runtime if called.\n */\nclass PrismaClient {\n  constructor() {\n    return new Proxy(this, {\n      get(target, prop) {\n        let message\n        const runtime = getRuntime()\n        if (runtime.isEdge) {\n          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:\n- Use Prisma Accelerate: https://pris.ly/d/accelerate\n- Use Driver Adapters: https://pris.ly/d/driver-adapters\n`;\n        } else {\n          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'\n        }\n        \n        message += `\nIf this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`\n\n        throw new Error(message)\n      }\n    })\n  }\n}\n\nexports.PrismaClient = PrismaClient\n\nObject.assign(exports, Prisma)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@prisma/client/index-browser.js":
/*!******************************************************!*\
  !*** ./node_modules/@prisma/client/index-browser.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("const prisma = __webpack_require__(/*! .prisma/client/index-browser */ \"(app-pages-browser)/./node_modules/.prisma/client/index-browser.js\")\n\nmodule.exports = prisma\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyx3R0FBOEI7O0FBRXJEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyLmpzP2E0Y2QiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcHJpc21hID0gcmVxdWlyZSgnLnByaXNtYS9jbGllbnQvaW5kZXgtYnJvd3NlcicpXG5cbm1vZHVsZS5leHBvcnRzID0gcHJpc21hXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js":
/*!**************************************************************!*\
  !*** ./node_modules/@prisma/client/runtime/index-browser.js ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("var de=Object.defineProperty;var We=Object.getOwnPropertyDescriptor;var Ge=Object.getOwnPropertyNames;var Je=Object.prototype.hasOwnProperty;var Me=(e,n)=>{for(var i in n)de(e,i,{get:n[i],enumerable:!0})},Xe=(e,n,i,t)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let r of Ge(n))!Je.call(e,r)&&r!==i&&de(e,r,{get:()=>n[r],enumerable:!(t=We(n,r))||t.enumerable});return e};var Ke=e=>Xe(de({},\"__esModule\",{value:!0}),e);var Xn={};Me(Xn,{Decimal:()=>je,Public:()=>he,getRuntime:()=>be,makeStrictEnum:()=>Pe,objectEnumValues:()=>Oe});module.exports=Ke(Xn);var he={};Me(he,{validator:()=>Ce});function Ce(...e){return n=>n}var ne=Symbol(),pe=new WeakMap,ge=class{constructor(n){n===ne?pe.set(this,\"Prisma.\".concat(this._getName())):pe.set(this,\"new Prisma.\".concat(this._getNamespace(),\".\").concat(this._getName(),\"()\"))}_getName(){return this.constructor.name}toString(){return pe.get(this)}},G=class extends ge{_getNamespace(){return\"NullTypes\"}},J=class extends G{};me(J,\"DbNull\");var X=class extends G{};me(X,\"JsonNull\");var K=class extends G{};me(K,\"AnyNull\");var Oe={classes:{DbNull:J,JsonNull:X,AnyNull:K},instances:{DbNull:new J(ne),JsonNull:new X(ne),AnyNull:new K(ne)}};function me(e,n){Object.defineProperty(e,\"name\",{value:n,configurable:!0})}var xe=new Set([\"toJSON\",\"$$typeof\",\"asymmetricMatch\",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Pe(e){return new Proxy(e,{get(n,i){if(i in n)return n[i];if(!xe.has(i))throw new TypeError(\"Invalid enum value: \".concat(String(i)))}})}var Qe=\"Cloudflare-Workers\",Ye=\"node\";function Re(){var e,n,i;return typeof Netlify==\"object\"?\"netlify\":typeof EdgeRuntime==\"string\"?\"edge-light\":((e=globalThis.navigator)==null?void 0:e.userAgent)===Qe?\"workerd\":globalThis.Deno?\"deno\":globalThis.__lagon__?\"lagon\":((i=(n=globalThis.process)==null?void 0:n.release)==null?void 0:i.name)===Ye?\"node\":globalThis.Bun?\"bun\":globalThis.fastly?\"fastly\":\"unknown\"}var ze={node:\"Node.js\",workerd:\"Cloudflare Workers\",deno:\"Deno and Deno Deploy\",netlify:\"Netlify Edge Functions\",\"edge-light\":\"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)\"};function be(){let e=Re();return{id:e,prettyName:ze[e]||e,isEdge:[\"workerd\",\"deno\",\"netlify\",\"edge-light\"].includes(e)}}var H=9e15,$=1e9,we=\"0123456789abcdef\",te=\"2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058\",re=\"3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789\",Ne={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-H,maxE:H,crypto:!1},Te,Z,w=!0,oe=\"[DecimalError] \",V=oe+\"Invalid argument: \",Le=oe+\"Precision limit exceeded\",De=oe+\"crypto unavailable\",Fe=\"[object Decimal]\",b=Math.floor,C=Math.pow,ye=/^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,en=/^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,nn=/^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,Ie=/^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,D=1e7,m=7,tn=9007199254740991,rn=te.length-1,ve=re.length-1,h={toStringTag:Fe};h.absoluteValue=h.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};h.ceil=function(){return p(new this.constructor(this),this.e+1,2)};h.clampedTo=h.clamp=function(e,n){var i,t=this,r=t.constructor;if(e=new r(e),n=new r(n),!e.s||!n.s)return new r(NaN);if(e.gt(n))throw Error(V+n);return i=t.cmp(e),i<0?e:t.cmp(n)>0?n:new r(t)};h.comparedTo=h.cmp=function(e){var n,i,t,r,s=this,o=s.d,u=(e=new s.constructor(e)).d,l=s.s,f=e.s;if(!o||!u)return!l||!f?NaN:l!==f?l:o===u?0:!o^l<0?1:-1;if(!o[0]||!u[0])return o[0]?l:u[0]?-f:0;if(l!==f)return l;if(s.e!==e.e)return s.e>e.e^l<0?1:-1;for(t=o.length,r=u.length,n=0,i=t<r?t:r;n<i;++n)if(o[n]!==u[n])return o[n]>u[n]^l<0?1:-1;return t===r?0:t>r^l<0?1:-1};h.cosine=h.cos=function(){var e,n,i=this,t=i.constructor;return i.d?i.d[0]?(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=sn(t,$e(t,i)),t.precision=e,t.rounding=n,p(Z==2||Z==3?i.neg():i,e,n,!0)):new t(1):new t(NaN)};h.cubeRoot=h.cbrt=function(){var e,n,i,t,r,s,o,u,l,f,c=this,a=c.constructor;if(!c.isFinite()||c.isZero())return new a(c);for(w=!1,s=c.s*C(c.s*c,1/3),!s||Math.abs(s)==1/0?(i=O(c.d),e=c.e,(s=(e-i.length+1)%3)&&(i+=s==1||s==-2?\"0\":\"00\"),s=C(i,1/3),e=b((e+1)/3)-(e%3==(e<0?-1:2)),s==1/0?i=\"5e\"+e:(i=s.toExponential(),i=i.slice(0,i.indexOf(\"e\")+1)+e),t=new a(i),t.s=c.s):t=new a(s.toString()),o=(e=a.precision)+3;;)if(u=t,l=u.times(u).times(u),f=l.plus(c),t=S(f.plus(c).times(u),f.plus(l),o+2,1),O(u.d).slice(0,o)===(i=O(t.d)).slice(0,o))if(i=i.slice(o-3,o+1),i==\"9999\"||!r&&i==\"4999\"){if(!r&&(p(u,e+1,0),u.times(u).times(u).eq(c))){t=u;break}o+=4,r=1}else{(!+i||!+i.slice(1)&&i.charAt(0)==\"5\")&&(p(t,e+1,1),n=!t.times(t).times(t).eq(c));break}return w=!0,p(t,e,a.rounding,n)};h.decimalPlaces=h.dp=function(){var e,n=this.d,i=NaN;if(n){if(e=n.length-1,i=(e-b(this.e/m))*m,e=n[e],e)for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i};h.dividedBy=h.div=function(e){return S(this,new this.constructor(e))};h.dividedToIntegerBy=h.divToInt=function(e){var n=this,i=n.constructor;return p(S(n,new i(e),0,1,1),i.precision,i.rounding)};h.equals=h.eq=function(e){return this.cmp(e)===0};h.floor=function(){return p(new this.constructor(this),this.e+1,3)};h.greaterThan=h.gt=function(e){return this.cmp(e)>0};h.greaterThanOrEqualTo=h.gte=function(e){var n=this.cmp(e);return n==1||n===0};h.hyperbolicCosine=h.cosh=function(){var e,n,i,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,r=s.d.length,r<32?(e=Math.ceil(r/3),n=(1/fe(4,e)).toString()):(e=16,n=\"2.3283064365386962890625e-10\"),s=j(o,1,s.times(n),new o(1),!0);for(var l,f=e,c=new o(8);f--;)l=s.times(s),s=u.minus(l.times(c.minus(l.times(c))));return p(s,o.precision=i,o.rounding=t,!0)};h.hyperbolicSine=h.sinh=function(){var e,n,i,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(n=s.precision,i=s.rounding,s.precision=n+Math.max(r.e,r.sd())+4,s.rounding=1,t=r.d.length,t<3)r=j(s,2,r,r,!0);else{e=1.4*Math.sqrt(t),e=e>16?16:e|0,r=r.times(1/fe(5,e)),r=j(s,2,r,r,!0);for(var o,u=new s(5),l=new s(16),f=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(l.times(o).plus(f))))}return s.precision=n,s.rounding=i,p(r,n,i,!0)};h.hyperbolicTangent=h.tanh=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,S(i.sinh(),i.cosh(),t.precision=e,t.rounding=n)):new t(i.s)};h.inverseCosine=h.acos=function(){var e,n=this,i=n.constructor,t=n.abs().cmp(1),r=i.precision,s=i.rounding;return t!==-1?t===0?n.isNeg()?L(i,r,s):new i(0):new i(NaN):n.isZero()?L(i,r+4,s).times(.5):(i.precision=r+6,i.rounding=1,n=n.asin(),e=L(i,r+4,s).times(.5),i.precision=r,i.rounding=s,e.minus(n))};h.inverseHyperbolicCosine=h.acosh=function(){var e,n,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(e=t.precision,n=t.rounding,t.precision=e+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,w=!1,i=i.times(i).minus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln()):new t(i)};h.inverseHyperbolicSine=h.asinh=function(){var e,n,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,w=!1,i=i.times(i).plus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln())};h.inverseHyperbolicTangent=h.atanh=function(){var e,n,i,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,n=s.rounding,t=r.sd(),Math.max(t,e)<2*-r.e-1?p(new s(r),e,n,!0):(s.precision=i=t-r.e,r=S(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=n,r.times(.5))):new s(NaN)};h.inverseSine=h.asin=function(){var e,n,i,t,r=this,s=r.constructor;return r.isZero()?new s(r):(n=r.abs().cmp(1),i=s.precision,t=s.rounding,n!==-1?n===0?(e=L(s,i+4,t).times(.5),e.s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=t,r.times(2)))};h.inverseTangent=h.atan=function(){var e,n,i,t,r,s,o,u,l,f=this,c=f.constructor,a=c.precision,d=c.rounding;if(f.isFinite()){if(f.isZero())return new c(f);if(f.abs().eq(1)&&a+4<=ve)return o=L(c,a+4,d).times(.25),o.s=f.s,o}else{if(!f.s)return new c(NaN);if(a+4<=ve)return o=L(c,a+4,d).times(.5),o.s=f.s,o}for(c.precision=u=a+10,c.rounding=1,i=Math.min(28,u/m+2|0),e=i;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(w=!1,n=Math.ceil(u/m),t=1,l=f.times(f),o=new c(f),r=f;e!==-1;)if(r=r.times(l),s=o.minus(r.div(t+=2)),r=r.times(l),o=s.plus(r.div(t+=2)),o.d[n]!==void 0)for(e=n;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),w=!0,p(o,c.precision=a,c.rounding=d,!0)};h.isFinite=function(){return!!this.d};h.isInteger=h.isInt=function(){return!!this.d&&b(this.e/m)>this.d.length-2};h.isNaN=function(){return!this.s};h.isNegative=h.isNeg=function(){return this.s<0};h.isPositive=h.isPos=function(){return this.s>0};h.isZero=function(){return!!this.d&&this.d[0]===0};h.lessThan=h.lt=function(e){return this.cmp(e)<0};h.lessThanOrEqualTo=h.lte=function(e){return this.cmp(e)<1};h.logarithm=h.log=function(e){var n,i,t,r,s,o,u,l,f=this,c=f.constructor,a=c.precision,d=c.rounding,g=5;if(e==null)e=new c(10),n=!0;else{if(e=new c(e),i=e.d,e.s<0||!i||!i[0]||e.eq(1))return new c(NaN);n=e.eq(10)}if(i=f.d,f.s<0||!i||!i[0]||f.eq(1))return new c(i&&!i[0]?-1/0:f.s!=1?NaN:i?0:1/0);if(n)if(i.length>1)s=!0;else{for(r=i[0];r%10===0;)r/=10;s=r!==1}if(w=!1,u=a+g,o=B(f,u),t=n?se(c,u+10):B(e,u),l=S(o,t,u,1),x(l.d,r=a,d))do if(u+=10,o=B(f,u),t=n?se(c,u+10):B(e,u),l=S(o,t,u,1),!s){+O(l.d).slice(r+1,r+15)+1==1e14&&(l=p(l,a+1,0));break}while(x(l.d,r+=10,d));return w=!0,p(l,a,d)};h.minus=h.sub=function(e){var n,i,t,r,s,o,u,l,f,c,a,d,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,d=e.d,u=v.precision,l=v.rounding,!f[0]||!d[0]){if(d[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(l===3?-0:0);return w?p(e,u,l):e}if(i=b(e.e/m),c=b(g.e/m),f=f.slice(),s=c-i,s){for(a=s<0,a?(n=f,s=-s,o=d.length):(n=d,i=c,o=f.length),t=Math.max(Math.ceil(u/m),o)+2,s>t&&(s=t,n.length=1),n.reverse(),t=s;t--;)n.push(0);n.reverse()}else{for(t=f.length,o=d.length,a=t<o,a&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){a=f[t]<d[t];break}s=0}for(a&&(n=f,f=d,d=n,e.s=-e.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>s;){if(f[--t]<d[t]){for(r=t;r&&f[--r]===0;)f[r]=D-1;--f[r],f[t]+=D}f[t]-=d[t]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--i;return f[0]?(e.d=f,e.e=ue(f,i),w?p(e,u,l):e):new v(l===3?-0:0)};h.modulo=h.mod=function(e){var n,i=this,t=i.constructor;return e=new t(e),!i.d||!e.s||e.d&&!e.d[0]?new t(NaN):!e.d||i.d&&!i.d[0]?p(new t(i),t.precision,t.rounding):(w=!1,t.modulo==9?(n=S(i,e.abs(),0,3,1),n.s*=e.s):n=S(i,e,0,t.modulo,1),n=n.times(e),w=!0,i.minus(n))};h.naturalExponential=h.exp=function(){return Ee(this)};h.naturalLogarithm=h.ln=function(){return B(this)};h.negated=h.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};h.plus=h.add=function(e){var n,i,t,r,s,o,u,l,f,c,a=this,d=a.constructor;if(e=new d(e),!a.d||!e.d)return!a.s||!e.s?e=new d(NaN):a.d||(e=new d(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,c=e.d,u=d.precision,l=d.rounding,!f[0]||!c[0])return c[0]||(e=new d(a)),w?p(e,u,l):e;if(s=b(a.e/m),t=b(e.e/m),f=f.slice(),r=s-t,r){for(r<0?(i=f,r=-r,o=c.length):(i=c,t=s,o=f.length),s=Math.ceil(u/m),o=s>o?s+1:o+1,r>o&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for(o=f.length,r=c.length,o-r<0&&(r=o,i=c,c=f,f=i),n=0;r;)n=(f[--r]=f[r]+c[r]+n)/D|0,f[r]%=D;for(n&&(f.unshift(n),++t),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=ue(f,t),w?p(e,u,l):e};h.precision=h.sd=function(e){var n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(V+e);return i.d?(n=Ze(i.d),e&&i.e+1>n&&(n=i.e+1)):n=NaN,n};h.round=function(){var e=this,n=e.constructor;return p(new n(e),e.e+1,n.rounding)};h.sine=h.sin=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=un(t,$e(t,i)),t.precision=e,t.rounding=n,p(Z>2?i.neg():i,e,n,!0)):new t(NaN)};h.squareRoot=h.sqrt=function(){var e,n,i,t,r,s,o=this,u=o.d,l=o.e,f=o.s,c=o.constructor;if(f!==1||!u||!u[0])return new c(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(w=!1,f=Math.sqrt(+o),f==0||f==1/0?(n=O(u),(n.length+l)%2==0&&(n+=\"0\"),f=Math.sqrt(n),l=b((l+1)/2)-(l<0||l%2),f==1/0?n=\"5e\"+l:(n=f.toExponential(),n=n.slice(0,n.indexOf(\"e\")+1)+l),t=new c(n)):t=new c(f.toString()),i=(l=c.precision)+3;;)if(s=t,t=s.plus(S(o,s,i+2,1)).times(.5),O(s.d).slice(0,i)===(n=O(t.d)).slice(0,i))if(n=n.slice(i-3,i+1),n==\"9999\"||!r&&n==\"4999\"){if(!r&&(p(s,l+1,0),s.times(s).eq(o))){t=s;break}i+=4,r=1}else{(!+n||!+n.slice(1)&&n.charAt(0)==\"5\")&&(p(t,l+1,1),e=!t.times(t).eq(o));break}return w=!0,p(t,l,c.rounding,e)};h.tangent=h.tan=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+10,t.rounding=1,i=i.sin(),i.s=1,i=S(i,new t(1).minus(i.times(i)).sqrt(),e+10,0),t.precision=e,t.rounding=n,p(Z==2||Z==4?i.neg():i,e,n,!0)):new t(NaN)};h.times=h.mul=function(e){var n,i,t,r,s,o,u,l,f,c=this,a=c.constructor,d=c.d,g=(e=new a(e)).d;if(e.s*=c.s,!d||!d[0]||!g||!g[0])return new a(!e.s||d&&!d[0]&&!g||g&&!g[0]&&!d?NaN:!d||!g?e.s/0:e.s*0);for(i=b(c.e/m)+b(e.e/m),l=d.length,f=g.length,l<f&&(s=d,d=g,g=s,o=l,l=f,f=o),s=[],o=l+f,t=o;t--;)s.push(0);for(t=f;--t>=0;){for(n=0,r=l+t;r>t;)u=s[r]+g[t]*d[r-t-1]+n,s[r--]=u%D|0,n=u/D|0;s[r]=(s[r]+n)%D|0}for(;!s[--o];)s.pop();return n?++i:s.shift(),e.d=s,e.e=ue(s,i),w?p(e,a.precision,a.rounding):e};h.toBinary=function(e,n){return ke(this,2,e,n)};h.toDecimalPlaces=h.toDP=function(e,n){var i=this,t=i.constructor;return i=new t(i),e===void 0?i:(_(e,0,$),n===void 0?n=t.rounding:_(n,0,8),p(i,e+i.e+1,n))};h.toExponential=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=F(t,!0):(_(e,0,$),n===void 0?n=r.rounding:_(n,0,8),t=p(new r(t),e+1,n),i=F(t,!0,e+1)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toFixed=function(e,n){var i,t,r=this,s=r.constructor;return e===void 0?i=F(r):(_(e,0,$),n===void 0?n=s.rounding:_(n,0,8),t=p(new s(r),e+r.e+1,n),i=F(t,!1,e+t.e+1)),r.isNeg()&&!r.isZero()?\"-\"+i:i};h.toFraction=function(e){var n,i,t,r,s,o,u,l,f,c,a,d,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=i=new N(1),t=l=new N(0),n=new N(t),s=n.e=Ze(v)-g.e-1,o=s%m,n.d[0]=C(10,o<0?m+o:o),e==null)e=s>0?n:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error(V+u);e=u.gt(n)?s>0?n:f:u}for(w=!1,u=new N(O(v)),c=N.precision,N.precision=s=v.length*m*2;a=S(u,n,0,1,1),r=i.plus(a.times(t)),r.cmp(e)!=1;)i=t,t=r,r=f,f=l.plus(a.times(r)),l=r,r=n,n=u.minus(a.times(r)),u=r;return r=S(e.minus(i),t,0,1,1),l=l.plus(r.times(f)),i=i.plus(r.times(t)),l.s=f.s=g.s,d=S(f,t,s,1).minus(g).abs().cmp(S(l,i,s,1).minus(g).abs())<1?[f,t]:[l,i],N.precision=c,w=!0,d};h.toHexadecimal=h.toHex=function(e,n){return ke(this,16,e,n)};h.toNearest=function(e,n){var i=this,t=i.constructor;if(i=new t(i),e==null){if(!i.d)return i;e=new t(1),n=t.rounding}else{if(e=new t(e),n===void 0?n=t.rounding:_(n,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(w=!1,i=S(i,e,0,n,1).times(e),w=!0,p(i)):(e.s=i.s,i=e),i};h.toNumber=function(){return+this};h.toOctal=function(e,n){return ke(this,8,e,n)};h.toPower=h.pow=function(e){var n,i,t,r,s,o,u=this,l=u.constructor,f=+(e=new l(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new l(C(+u,f));if(u=new l(u),u.eq(1))return u;if(t=l.precision,s=l.rounding,e.eq(1))return p(u,t,s);if(n=b(e.e/m),n>=e.d.length-1&&(i=f<0?-f:f)<=tn)return r=Ue(l,u,i,t),e.s<0?new l(1).div(r):p(r,t,s);if(o=u.s,o<0){if(n<e.d.length-1)return new l(NaN);if(e.d[n]&1||(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return i=C(+u,f),n=i==0||!isFinite(i)?b(f*(Math.log(\"0.\"+O(u.d))/Math.LN10+u.e+1)):new l(i+\"\").e,n>l.maxE+1||n<l.minE-1?new l(n>0?o/0:0):(w=!1,l.rounding=u.s=1,i=Math.min(12,(n+\"\").length),r=Ee(e.times(B(u,t+i)),t),r.d&&(r=p(r,t+5,1),x(r.d,t,s)&&(n=t+10,r=p(Ee(e.times(B(u,n+i)),n),n+5,1),+O(r.d).slice(t+1,t+15)+1==1e14&&(r=p(r,t+1,0)))),r.s=o,w=!0,l.rounding=s,p(r,t,s))};h.toPrecision=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=F(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(_(e,1,$),n===void 0?n=r.rounding:_(n,0,8),t=p(new r(t),e,n),i=F(t,e<=t.e||t.e<=r.toExpNeg,e)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toSignificantDigits=h.toSD=function(e,n){var i=this,t=i.constructor;return e===void 0?(e=t.precision,n=t.rounding):(_(e,1,$),n===void 0?n=t.rounding:_(n,0,8)),p(new t(i),e,n)};h.toString=function(){var e=this,n=e.constructor,i=F(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?\"-\"+i:i};h.truncated=h.trunc=function(){return p(new this.constructor(this),this.e+1,1)};h.valueOf=h.toJSON=function(){var e=this,n=e.constructor,i=F(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?\"-\"+i:i};function O(e){var n,i,t,r=e.length-1,s=\"\",o=e[0];if(r>0){for(s+=o,n=1;n<r;n++)t=e[n]+\"\",i=m-t.length,i&&(s+=U(i)),s+=t;o=e[n],t=o+\"\",i=m-t.length,i&&(s+=U(i))}else if(o===0)return\"0\";for(;o%10===0;)o/=10;return s+o}function _(e,n,i){if(e!==~~e||e<n||e>i)throw Error(V+e)}function x(e,n,i,t){var r,s,o,u;for(s=e[0];s>=10;s/=10)--n;return--n<0?(n+=m,r=0):(r=Math.ceil((n+1)/m),n%=m),s=C(10,m-n),u=e[r]%s|0,t==null?n<3?(n==0?u=u/100|0:n==1&&(u=u/10|0),o=i<4&&u==99999||i>3&&u==49999||u==5e4||u==0):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(e[r+1]/s/100|0)==C(10,n-2)-1||(u==s/2||u==0)&&(e[r+1]/s/100|0)==0:n<4?(n==0?u=u/1e3|0:n==1?u=u/100|0:n==2&&(u=u/10|0),o=(t||i<4)&&u==9999||!t&&i>3&&u==4999):o=((t||i<4)&&u+1==s||!t&&i>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==C(10,n-3)-1,o}function ie(e,n,i){for(var t,r=[0],s,o=0,u=e.length;o<u;){for(s=r.length;s--;)r[s]*=n;for(r[0]+=we.indexOf(e.charAt(o++)),t=0;t<r.length;t++)r[t]>i-1&&(r[t+1]===void 0&&(r[t+1]=0),r[t+1]+=r[t]/i|0,r[t]%=i)}return r.reverse()}function sn(e,n){var i,t,r;if(n.isZero())return n;t=n.d.length,t<32?(i=Math.ceil(t/3),r=(1/fe(4,i)).toString()):(i=16,r=\"2.3283064365386962890625e-10\"),e.precision+=i,n=j(e,1,n.times(r),new e(1));for(var s=i;s--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,n}var S=function(){function e(t,r,s){var o,u=0,l=t.length;for(t=t.slice();l--;)o=t[l]*r+u,t[l]=o%s|0,u=o/s|0;return u&&t.unshift(u),t}function n(t,r,s,o){var u,l;if(s!=o)l=s>o?1:-1;else for(u=l=0;u<s;u++)if(t[u]!=r[u]){l=t[u]>r[u]?1:-1;break}return l}function i(t,r,s,o){for(var u=0;s--;)t[s]-=u,u=t[s]<r[s]?1:0,t[s]=u*o+t[s]-r[s];for(;!t[0]&&t.length>1;)t.shift()}return function(t,r,s,o,u,l){var f,c,a,d,g,v,N,A,M,q,E,P,Y,I,le,z,W,ce,T,y,ee=t.constructor,ae=t.s==r.s?1:-1,R=t.d,k=r.d;if(!R||!R[0]||!k||!k[0])return new ee(!t.s||!r.s||(R?k&&R[0]==k[0]:!k)?NaN:R&&R[0]==0||!k?ae*0:ae/0);for(l?(g=1,c=t.e-r.e):(l=D,g=m,c=b(t.e/g)-b(r.e/g)),T=k.length,W=R.length,M=new ee(ae),q=M.d=[],a=0;k[a]==(R[a]||0);a++);if(k[a]>(R[a]||0)&&c--,s==null?(I=s=ee.precision,o=ee.rounding):u?I=s+(t.e-r.e)+1:I=s,I<0)q.push(1),v=!0;else{if(I=I/g+2|0,a=0,T==1){for(d=0,k=k[0],I++;(a<W||d)&&I--;a++)le=d*l+(R[a]||0),q[a]=le/k|0,d=le%k|0;v=d||a<W}else{for(d=l/(k[0]+1)|0,d>1&&(k=e(k,d,l),R=e(R,d,l),T=k.length,W=R.length),z=T,E=R.slice(0,T),P=E.length;P<T;)E[P++]=0;y=k.slice(),y.unshift(0),ce=k[0],k[1]>=l/2&&++ce;do d=0,f=n(k,E,T,P),f<0?(Y=E[0],T!=P&&(Y=Y*l+(E[1]||0)),d=Y/ce|0,d>1?(d>=l&&(d=l-1),N=e(k,d,l),A=N.length,P=E.length,f=n(N,E,A,P),f==1&&(d--,i(N,T<A?y:k,A,l))):(d==0&&(f=d=1),N=k.slice()),A=N.length,A<P&&N.unshift(0),i(E,N,P,l),f==-1&&(P=E.length,f=n(k,E,T,P),f<1&&(d++,i(E,T<P?y:k,P,l))),P=E.length):f===0&&(d++,E=[0]),q[a++]=d,f&&E[0]?E[P++]=R[z]||0:(E=[R[z]],P=1);while((z++<W||E[0]!==void 0)&&I--);v=E[0]!==void 0}q[0]||q.shift()}if(g==1)M.e=c,Te=v;else{for(a=1,d=q[0];d>=10;d/=10)a++;M.e=a+c*g-1,p(M,u?s+M.e+1:s,o,v)}return M}}();function p(e,n,i,t){var r,s,o,u,l,f,c,a,d,g=e.constructor;e:if(n!=null){if(a=e.d,!a)return e;for(r=1,u=a[0];u>=10;u/=10)r++;if(s=n-r,s<0)s+=m,o=n,c=a[d=0],l=c/C(10,r-o-1)%10|0;else if(d=Math.ceil((s+1)/m),u=a.length,d>=u)if(t){for(;u++<=d;)a.push(0);c=l=0,r=1,s%=m,o=s-m+1}else break e;else{for(c=u=a[d],r=1;u>=10;u/=10)r++;s%=m,o=s-m+r,l=o<0?0:c/C(10,r-o-1)%10|0}if(t=t||n<0||a[d+1]!==void 0||(o<0?c:c%C(10,r-o-1)),f=i<4?(l||t)&&(i==0||i==(e.s<0?3:2)):l>5||l==5&&(i==4||t||i==6&&(s>0?o>0?c/C(10,r-o):0:a[d-1])%10&1||i==(e.s<0?8:7)),n<1||!a[0])return a.length=0,f?(n-=e.e+1,a[0]=C(10,(m-n%m)%m),e.e=-n||0):a[0]=e.e=0,e;if(s==0?(a.length=d,u=1,d--):(a.length=d+1,u=C(10,m-s),a[d]=o>0?(c/C(10,r-o)%C(10,o)|0)*u:0),f)for(;;)if(d==0){for(s=1,o=a[0];o>=10;o/=10)s++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,a[0]==D&&(a[0]=1));break}else{if(a[d]+=u,a[d]!=D)break;a[d--]=0,u=1}for(s=a.length;a[--s]===0;)a.pop()}return w&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function F(e,n,i){if(!e.isFinite())return Ve(e);var t,r=e.e,s=O(e.d),o=s.length;return n?(i&&(t=i-o)>0?s=s.charAt(0)+\".\"+s.slice(1)+U(t):o>1&&(s=s.charAt(0)+\".\"+s.slice(1)),s=s+(e.e<0?\"e\":\"e+\")+e.e):r<0?(s=\"0.\"+U(-r-1)+s,i&&(t=i-o)>0&&(s+=U(t))):r>=o?(s+=U(r+1-o),i&&(t=i-r-1)>0&&(s=s+\".\"+U(t))):((t=r+1)<o&&(s=s.slice(0,t)+\".\"+s.slice(t)),i&&(t=i-o)>0&&(r+1===o&&(s+=\".\"),s+=U(t))),s}function ue(e,n){var i=e[0];for(n*=m;i>=10;i/=10)n++;return n}function se(e,n,i){if(n>rn)throw w=!0,i&&(e.precision=i),Error(Le);return p(new e(te),n,1,!0)}function L(e,n,i){if(n>ve)throw Error(Le);return p(new e(re),n,i,!0)}function Ze(e){var n=e.length-1,i=n*m+1;if(n=e[n],n){for(;n%10==0;n/=10)i--;for(n=e[0];n>=10;n/=10)i++}return i}function U(e){for(var n=\"\";e--;)n+=\"0\";return n}function Ue(e,n,i,t){var r,s=new e(1),o=Math.ceil(t/m+4);for(w=!1;;){if(i%2&&(s=s.times(n),_e(s.d,o)&&(r=!0)),i=b(i/2),i===0){i=s.d.length-1,r&&s.d[i]===0&&++s.d[i];break}n=n.times(n),_e(n.d,o)}return w=!0,s}function Ae(e){return e.d[e.d.length-1]&1}function Be(e,n,i){for(var t,r=new e(n[0]),s=0;++s<n.length;)if(t=new e(n[s]),t.s)r[i](t)&&(r=t);else{r=t;break}return r}function Ee(e,n){var i,t,r,s,o,u,l,f=0,c=0,a=0,d=e.constructor,g=d.rounding,v=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(n==null?(w=!1,l=v):l=n,u=new d(.03125);e.e>-2;)e=e.times(u),a+=5;for(t=Math.log(C(2,a))/Math.LN10*2+5|0,l+=t,i=s=o=new d(1),d.precision=l;;){if(s=p(s.times(e),l,1),i=i.times(++c),u=o.plus(S(s,i,l,1)),O(u.d).slice(0,l)===O(o.d).slice(0,l)){for(r=a;r--;)o=p(o.times(o),l,1);if(n==null)if(f<3&&x(o.d,l-t,g,f))d.precision=l+=10,i=s=u=new d(1),c=0,f++;else return p(o,d.precision=v,g,w=!0);else return d.precision=v,o}o=u}}function B(e,n){var i,t,r,s,o,u,l,f,c,a,d,g=1,v=10,N=e,A=N.d,M=N.constructor,q=M.rounding,E=M.precision;if(N.s<0||!A||!A[0]||!N.e&&A[0]==1&&A.length==1)return new M(A&&!A[0]?-1/0:N.s!=1?NaN:A?0:N);if(n==null?(w=!1,c=E):c=n,M.precision=c+=v,i=O(A),t=i.charAt(0),Math.abs(s=N.e)<15e14){for(;t<7&&t!=1||t==1&&i.charAt(1)>3;)N=N.times(e),i=O(N.d),t=i.charAt(0),g++;s=N.e,t>1?(N=new M(\"0.\"+i),s++):N=new M(t+\".\"+i.slice(1))}else return f=se(M,c+2,E).times(s+\"\"),N=B(new M(t+\".\"+i.slice(1)),c-v).plus(f),M.precision=E,n==null?p(N,E,q,w=!0):N;for(a=N,l=o=N=S(N.minus(1),N.plus(1),c,1),d=p(N.times(N),c,1),r=3;;){if(o=p(o.times(d),c,1),f=l.plus(S(o,new M(r),c,1)),O(f.d).slice(0,c)===O(l.d).slice(0,c))if(l=l.times(2),s!==0&&(l=l.plus(se(M,c+2,E).times(s+\"\"))),l=S(l,new M(g),c,1),n==null)if(x(l.d,c-v,q,u))M.precision=c+=v,f=o=N=S(a.minus(1),a.plus(1),c,1),d=p(N.times(N),c,1),r=u=1;else return p(l,M.precision=E,q,w=!0);else return M.precision=E,l;l=f,r+=2}}function Ve(e){return String(e.s*e.s/0)}function Se(e,n){var i,t,r;for((i=n.indexOf(\".\"))>-1&&(n=n.replace(\".\",\"\")),(t=n.search(/e/i))>0?(i<0&&(i=t),i+=+n.slice(t+1),n=n.substring(0,t)):i<0&&(i=n.length),t=0;n.charCodeAt(t)===48;t++);for(r=n.length;n.charCodeAt(r-1)===48;--r);if(n=n.slice(t,r),n){if(r-=t,e.e=i=i-t-1,e.d=[],t=(i+1)%m,i<0&&(t+=m),t<r){for(t&&e.d.push(+n.slice(0,t)),r-=m;t<r;)e.d.push(+n.slice(t,t+=m));n=n.slice(t),t=m-n.length}else t-=r;for(;t--;)n+=\"0\";e.d.push(+n),w&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function on(e,n){var i,t,r,s,o,u,l,f,c;if(n.indexOf(\"_\")>-1){if(n=n.replace(/(\\d)_(?=\\d)/g,\"$1\"),Ie.test(n))return Se(e,n)}else if(n===\"Infinity\"||n===\"NaN\")return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(en.test(n))i=16,n=n.toLowerCase();else if(ye.test(n))i=2;else if(nn.test(n))i=8;else throw Error(V+n);for(s=n.search(/p/i),s>0?(l=+n.slice(s+1),n=n.substring(2,s)):n=n.slice(2),s=n.indexOf(\".\"),o=s>=0,t=e.constructor,o&&(n=n.replace(\".\",\"\"),u=n.length,s=u-s,r=Ue(t,new t(i),s,s*2)),f=ie(n,i,D),c=f.length-1,s=c;f[s]===0;--s)f.pop();return s<0?new t(e.s*0):(e.e=ue(f,c),e.d=f,w=!1,o&&(e=S(e,r,u*4)),l&&(e=e.times(Math.abs(l)<54?C(2,l):Q.pow(2,l))),w=!0,e)}function un(e,n){var i,t=n.d.length;if(t<3)return n.isZero()?n:j(e,2,n,n);i=1.4*Math.sqrt(t),i=i>16?16:i|0,n=n.times(1/fe(5,i)),n=j(e,2,n,n);for(var r,s=new e(5),o=new e(16),u=new e(20);i--;)r=n.times(n),n=n.times(s.plus(r.times(o.times(r).minus(u))));return n}function j(e,n,i,t,r){var s,o,u,l,f=1,c=e.precision,a=Math.ceil(c/m);for(w=!1,l=i.times(i),u=new e(t);;){if(o=S(u.times(l),new e(n++*n++),c,1),u=r?t.plus(o):t.minus(o),t=S(o.times(l),new e(n++*n++),c,1),o=u.plus(t),o.d[a]!==void 0){for(s=a;o.d[s]===u.d[s]&&s--;);if(s==-1)break}s=u,u=t,t=o,o=s,f++}return w=!0,o.d.length=a+1,o}function fe(e,n){for(var i=e;--n;)i*=e;return i}function $e(e,n){var i,t=n.s<0,r=L(e,e.precision,1),s=r.times(.5);if(n=n.abs(),n.lte(s))return Z=t?4:1,n;if(i=n.divToInt(r),i.isZero())Z=t?3:2;else{if(n=n.minus(i.times(r)),n.lte(s))return Z=Ae(i)?t?2:3:t?4:1,n;Z=Ae(i)?t?1:4:t?3:2}return n.minus(r).abs()}function ke(e,n,i,t){var r,s,o,u,l,f,c,a,d,g=e.constructor,v=i!==void 0;if(v?(_(i,1,$),t===void 0?t=g.rounding:_(t,0,8)):(i=g.precision,t=g.rounding),!e.isFinite())c=Ve(e);else{for(c=F(e),o=c.indexOf(\".\"),v?(r=2,n==16?i=i*4-3:n==8&&(i=i*3-2)):r=n,o>=0&&(c=c.replace(\".\",\"\"),d=new g(1),d.e=c.length-o,d.d=ie(F(d),10,r),d.e=d.d.length),a=ie(c,10,r),s=l=a.length;a[--l]==0;)a.pop();if(!a[0])c=v?\"0p+0\":\"0\";else{if(o<0?s--:(e=new g(e),e.d=a,e.e=s,e=S(e,d,i,t,0,r),a=e.d,s=e.e,f=Te),o=a[i],u=r/2,f=f||a[i+1]!==void 0,f=t<4?(o!==void 0||f)&&(t===0||t===(e.s<0?3:2)):o>u||o===u&&(t===4||f||t===6&&a[i-1]&1||t===(e.s<0?8:7)),a.length=i,f)for(;++a[--i]>r-1;)a[i]=0,i||(++s,a.unshift(1));for(l=a.length;!a[l-1];--l);for(o=0,c=\"\";o<l;o++)c+=we.charAt(a[o]);if(v){if(l>1)if(n==16||n==8){for(o=n==16?4:3,--l;l%o;l++)c+=\"0\";for(a=ie(c,r,n),l=a.length;!a[l-1];--l);for(o=1,c=\"1.\";o<l;o++)c+=we.charAt(a[o])}else c=c.charAt(0)+\".\"+c.slice(1);c=c+(s<0?\"p\":\"p+\")+s}else if(s<0){for(;++s;)c=\"0\"+c;c=\"0.\"+c}else if(++s>l)for(s-=l;s--;)c+=\"0\";else s<l&&(c=c.slice(0,s)+\".\"+c.slice(s))}c=(n==16?\"0x\":n==2?\"0b\":n==8?\"0o\":\"\")+c}return e.s<0?\"-\"+c:c}function _e(e,n){if(e.length>n)return e.length=n,!0}function fn(e){return new this(e).abs()}function ln(e){return new this(e).acos()}function cn(e){return new this(e).acosh()}function an(e,n){return new this(e).plus(n)}function dn(e){return new this(e).asin()}function hn(e){return new this(e).asinh()}function pn(e){return new this(e).atan()}function gn(e){return new this(e).atanh()}function mn(e,n){e=new this(e),n=new this(n);var i,t=this.precision,r=this.rounding,s=t+4;return!e.s||!n.s?i=new this(NaN):!e.d&&!n.d?(i=L(this,s,1).times(n.s>0?.25:.75),i.s=e.s):!n.d||e.isZero()?(i=n.s<0?L(this,t,r):new this(0),i.s=e.s):!e.d||n.isZero()?(i=L(this,s,1).times(.5),i.s=e.s):n.s<0?(this.precision=s,this.rounding=1,i=this.atan(S(e,n,s,1)),n=L(this,s,1),this.precision=t,this.rounding=r,i=e.s<0?i.minus(n):i.plus(n)):i=this.atan(S(e,n,s,1)),i}function wn(e){return new this(e).cbrt()}function Nn(e){return p(e=new this(e),e.e+1,2)}function vn(e,n,i){return new this(e).clamp(n,i)}function En(e){if(!e||typeof e!=\"object\")throw Error(oe+\"Object expected\");var n,i,t,r=e.defaults===!0,s=[\"precision\",1,$,\"rounding\",0,8,\"toExpNeg\",-H,0,\"toExpPos\",0,H,\"maxE\",0,H,\"minE\",-H,0,\"modulo\",0,9];for(n=0;n<s.length;n+=3)if(i=s[n],r&&(this[i]=Ne[i]),(t=e[i])!==void 0)if(b(t)===t&&t>=s[n+1]&&t<=s[n+2])this[i]=t;else throw Error(V+i+\": \"+t);if(i=\"crypto\",r&&(this[i]=Ne[i]),(t=e[i])!==void 0)if(t===!0||t===!1||t===0||t===1)if(t)if(typeof crypto<\"u\"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(De);else this[i]=!1;else throw Error(V+i+\": \"+t);return this}function Sn(e){return new this(e).cos()}function kn(e){return new this(e).cosh()}function He(e){var n,i,t;function r(s){var o,u,l,f=this;if(!(f instanceof r))return new r(s);if(f.constructor=r,qe(s)){f.s=s.s,w?!s.d||s.e>r.maxE?(f.e=NaN,f.d=null):s.e<r.minE?(f.e=0,f.d=[0]):(f.e=s.e,f.d=s.d.slice()):(f.e=s.e,f.d=s.d?s.d.slice():s.d);return}if(l=typeof s,l===\"number\"){if(s===0){f.s=1/s<0?-1:1,f.e=0,f.d=[0];return}if(s<0?(s=-s,f.s=-1):f.s=1,s===~~s&&s<1e7){for(o=0,u=s;u>=10;u/=10)o++;w?o>r.maxE?(f.e=NaN,f.d=null):o<r.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[s]):(f.e=o,f.d=[s]);return}else if(s*0!==0){s||(f.s=NaN),f.e=NaN,f.d=null;return}return Se(f,s.toString())}else if(l!==\"string\")throw Error(V+s);return(u=s.charCodeAt(0))===45?(s=s.slice(1),f.s=-1):(u===43&&(s=s.slice(1)),f.s=1),Ie.test(s)?Se(f,s):on(f,s)}if(r.prototype=h,r.ROUND_UP=0,r.ROUND_DOWN=1,r.ROUND_CEIL=2,r.ROUND_FLOOR=3,r.ROUND_HALF_UP=4,r.ROUND_HALF_DOWN=5,r.ROUND_HALF_EVEN=6,r.ROUND_HALF_CEIL=7,r.ROUND_HALF_FLOOR=8,r.EUCLID=9,r.config=r.set=En,r.clone=He,r.isDecimal=qe,r.abs=fn,r.acos=ln,r.acosh=cn,r.add=an,r.asin=dn,r.asinh=hn,r.atan=pn,r.atanh=gn,r.atan2=mn,r.cbrt=wn,r.ceil=Nn,r.clamp=vn,r.cos=Sn,r.cosh=kn,r.div=Mn,r.exp=Cn,r.floor=On,r.hypot=Pn,r.ln=Rn,r.log=bn,r.log10=_n,r.log2=An,r.max=qn,r.min=Tn,r.mod=Ln,r.mul=Dn,r.pow=Fn,r.random=In,r.round=Zn,r.sign=Un,r.sin=Bn,r.sinh=Vn,r.sqrt=$n,r.sub=Hn,r.sum=jn,r.tan=Wn,r.tanh=Gn,r.trunc=Jn,e===void 0&&(e={}),e&&e.defaults!==!0)for(t=[\"precision\",\"rounding\",\"toExpNeg\",\"toExpPos\",\"maxE\",\"minE\",\"modulo\",\"crypto\"],n=0;n<t.length;)e.hasOwnProperty(i=t[n++])||(e[i]=this[i]);return r.config(e),r}function Mn(e,n){return new this(e).div(n)}function Cn(e){return new this(e).exp()}function On(e){return p(e=new this(e),e.e+1,3)}function Pn(){var e,n,i=new this(0);for(w=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)i.d&&(i=i.plus(n.times(n)));else{if(n.s)return w=!0,new this(1/0);i=n}return w=!0,i.sqrt()}function qe(e){return e instanceof Q||e&&e.toStringTag===Fe||!1}function Rn(e){return new this(e).ln()}function bn(e,n){return new this(e).log(n)}function An(e){return new this(e).log(2)}function _n(e){return new this(e).log(10)}function qn(){return Be(this,arguments,\"lt\")}function Tn(){return Be(this,arguments,\"gt\")}function Ln(e,n){return new this(e).mod(n)}function Dn(e,n){return new this(e).mul(n)}function Fn(e,n){return new this(e).pow(n)}function In(e){var n,i,t,r,s=0,o=new this(1),u=[];if(e===void 0?e=this.precision:_(e,1,$),t=Math.ceil(e/m),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(t));s<t;)r=n[s],r>=429e7?n[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(t*=4);s<t;)r=n[s]+(n[s+1]<<8)+(n[s+2]<<16)+((n[s+3]&127)<<24),r>=214e7?crypto.randomBytes(4).copy(n,s):(u.push(r%1e7),s+=4);s=t/4}else throw Error(De);else for(;s<t;)u[s++]=Math.random()*1e7|0;for(t=u[--s],e%=m,t&&e&&(r=C(10,m-e),u[s]=(t/r|0)*r);u[s]===0;s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;u[0]===0;i-=m)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<m&&(i-=m-t)}return o.e=i,o.d=u,o}function Zn(e){return p(e=new this(e),e.e+1,this.rounding)}function Un(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Bn(e){return new this(e).sin()}function Vn(e){return new this(e).sinh()}function $n(e){return new this(e).sqrt()}function Hn(e,n){return new this(e).sub(n)}function jn(){var e=0,n=arguments,i=new this(n[e]);for(w=!1;i.s&&++e<n.length;)i=i.plus(n[e]);return w=!0,p(i,this.precision,this.rounding)}function Wn(e){return new this(e).tan()}function Gn(e){return new this(e).tanh()}function Jn(e){return p(e=new this(e),e.e+1,1)}h[Symbol.for(\"nodejs.util.inspect.custom\")]=h.toString;h[Symbol.toStringTag]=\"Decimal\";var Q=h.constructor=He(Ne);te=new Q(te);re=new Q(re);var je=Q;0&&(0);\n/*! Bundled license information:\n\ndecimal.js/decimal.mjs:\n  (*!\n   *  decimal.js v10.4.3\n   *  An arbitrary-precision Decimal type for JavaScript.\n   *  https://github.com/MikeMcl/decimal.js\n   *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>\n   *  MIT Licence\n   *)\n*/\n//# sourceMappingURL=index-browser.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/authentication/auth/AuthLogin.tsx":
/*!***************************************************!*\
  !*** ./src/app/authentication/auth/AuthLogin.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Checkbox/Checkbox.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField */ \"(app-pages-browser)/./src/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AuthLogin = (param)=>{\n    let { title, subtitle, subtext } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        if (!email || !password) {\n            setError(\"Please enter both email and password\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const result = await login(email, password);\n            if (result.success) {\n                router.push(\"/\");\n            } else {\n                setError(result.error || \"Login failed\");\n            }\n        } catch (error) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                fontWeight: \"700\",\n                variant: \"h2\",\n                mb: 1,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, undefined) : null,\n            subtext,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 2\n                                },\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"subtitle1\",\n                                        fontWeight: 600,\n                                        component: \"label\",\n                                        htmlFor: \"email\",\n                                        mb: \"5px\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        id: \"email\",\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                mt: \"25px\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"subtitle1\",\n                                        fontWeight: 600,\n                                        component: \"label\",\n                                        htmlFor: \"password\",\n                                        mb: \"5px\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        id: \"password\",\n                                        type: \"password\",\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        value: password,\n                                        onChange: (e)=>setPassword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                justifyContent: \"space-between\",\n                                direction: \"row\",\n                                alignItems: \"center\",\n                                my: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                defaultChecked: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 26\n                                            }, void 0),\n                                            label: \"Remember this Device\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        component: next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                                        href: \"/\",\n                                        fontWeight: \"500\",\n                                        sx: {\n                                            textDecoration: \"none\",\n                                            color: \"primary.main\"\n                                        },\n                                        children: \"Forgot Password ?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            color: \"primary\",\n                            variant: \"contained\",\n                            size: \"large\",\n                            fullWidth: true,\n                            type: \"submit\",\n                            disabled: isLoading,\n                            startIcon: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 20,\n                                color: \"inherit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 36\n                            }, void 0) : null,\n                            children: isLoading ? \"Signing In...\" : \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            subtitle\n        ]\n    }, void 0, true);\n};\n_s(AuthLogin, \"/ZgK5cQl/GuM3r+ZdbKq0p+89AY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = AuthLogin;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AuthLogin);\nvar _c;\n$RefreshReg$(_c, \"AuthLogin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/authentication/auth/AuthLogin.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated\n    const isAuthenticated = !!user;\n    // Role hierarchy for permission checking\n    const roleHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.STUDENT]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.TEACHER]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.RECEPTION]: 2,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN]: 3\n    };\n    // Check if user has required permission\n    const hasPermission = (requiredRole)=>{\n        if (!user) return false;\n        return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n    };\n    // Login function\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setUser(data.user);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error occurred\"\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            // Redirect to login page\n            window.location.href = \"/authentication/login\";\n        }\n    };\n    // Refresh token function\n    const refreshToken = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.user);\n                    return true;\n                }\n            }\n            // If refresh fails, logout user\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Get current user on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getCurrentUser = async ()=>{\n            try {\n                const response = await fetch(\"/api/auth/me\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    }\n                } else if (response.status === 401) {\n                    // Try to refresh token\n                    const refreshSuccess = await refreshToken();\n                    if (!refreshSuccess) {\n                        // Redirect to login if refresh fails\n                        window.location.href = \"/authentication/login\";\n                    }\n                }\n            } catch (error) {\n                console.error(\"Get current user error:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getCurrentUser();\n    }, []);\n    // Auto-refresh token before expiry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(async ()=>{\n            await refreshToken();\n        }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"l8KTfcQ0b0tPgqlKoCac6dKortU=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});