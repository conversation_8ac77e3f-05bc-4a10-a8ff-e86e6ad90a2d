"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(DashboardLayout)/layout",{

/***/ "(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx":
/*!**********************************************!*\
  !*** ./src/app/(DashboardLayout)/layout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/header/Header */ \"(app-pages-browser)/./src/app/(DashboardLayout)/layout/header/Header.tsx\");\n/* harmony import */ var _app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/sidebar/Sidebar */ \"(app-pages-browser)/./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst MainWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\")(()=>({\n        display: \"flex\",\n        minHeight: \"100vh\",\n        width: \"100%\"\n    }));\n_c = MainWrapper;\nconst PageWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\", {\n    shouldForwardProp: (prop)=>prop !== \"isSidebarOpen\"\n})((param)=>{\n    let { theme, isSidebarOpen } = param;\n    return {\n        display: \"flex\",\n        flexGrow: 1,\n        paddingBottom: \"60px\",\n        flexDirection: \"column\",\n        zIndex: 1,\n        backgroundColor: \"transparent\",\n        marginLeft: \"0px\",\n        transition: \"all 0.2s ease-in-out\",\n        [theme.breakpoints.up(\"lg\")]: {\n            marginLeft: isSidebarOpen ? \"270px\" : \"0px\"\n        }\n    };\n});\n_c1 = PageWrapper;\nfunction RootLayout(param) {\n    let { children } = param;\n    _s();\n    const [isSidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobileSidebarOpen, setMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!isSidebarOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainWrapper, {\n            className: \"mainwrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    isSidebarOpen: isSidebarOpen,\n                    isMobileSidebarOpen: isMobileSidebarOpen,\n                    onSidebarClose: ()=>setMobileSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageWrapper, {\n                    className: \"page-wrapper\",\n                    isSidebarOpen: isSidebarOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            toggleMobileSidebar: ()=>setMobileSidebarOpen(true),\n                            toggleSidebar: toggleSidebar,\n                            isSidebarOpen: isSidebarOpen\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                paddingTop: \"20px\",\n                                maxWidth: \"1200px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    minHeight: \"calc(100vh - 170px)\"\n                                },\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(RootLayout, \"Vb6bppI4DuiAlAQexSsBaC+YFw0=\");\n_c2 = RootLayout;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MainWrapper\");\n$RefreshReg$(_c1, \"PageWrapper\");\n$RefreshReg$(_c2, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx\n"));

/***/ })

});