import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { attendanceSchema } from '@/lib/validation';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can view their class attendance, Admin/Reception can view all
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');
    const studentId = searchParams.get('studentId');
    const date = searchParams.get('date');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build filter conditions
    const where: any = {};
    
    if (classId) where.classId = classId;
    if (studentId) where.studentId = studentId;
    if (date) where.date = new Date(date);
    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    // If user is a teacher, only show attendance for their classes
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (teacher) {
        where.teacherId = teacher.id;
      }
    }

    const attendance = await prisma.attendance.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
      orderBy: [
        { date: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'READ',
      'Attendance',
      'query',
      null,
      { filters: where },
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(attendance);
  } catch (error) {
    console.error('Get attendance error:', error);
    return NextResponse.json({ error: 'Failed to fetch attendance' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can mark attendance for their classes, Admin/Reception can mark any
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate input
    const validationResult = attendanceSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        }, 
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // If user is a teacher, verify they can mark attendance for this class
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (!teacher || teacher.id !== data.teacherId) {
        return NextResponse.json({ error: 'You can only mark attendance for your own classes' }, { status: 403 });
      }
    }

    // Check if attendance already exists for this student, class, and date
    const existingAttendance = await prisma.attendance.findUnique({
      where: {
        studentId_classId_date: {
          studentId: data.studentId,
          classId: data.classId,
          date: new Date(data.date),
        },
      },
    });

    if (existingAttendance) {
      return NextResponse.json({ error: 'Attendance already marked for this date' }, { status: 400 });
    }

    const attendance = await prisma.attendance.create({
      data: {
        studentId: data.studentId,
        teacherId: data.teacherId,
        classId: data.classId,
        date: new Date(data.date),
        status: data.status,
        notes: data.notes,
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'CREATE',
      'Attendance',
      attendance.id,
      null,
      attendance,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(attendance);
  } catch (error) {
    console.error('Create attendance error:', error);
    return NextResponse.json({ error: 'Failed to create attendance record' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can update their class attendance, Admin/Reception can update any
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json({ error: 'Attendance ID is required' }, { status: 400 });
    }

    // Validate input
    const validationResult = attendanceSchema.partial().safeParse(updateData);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        }, 
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Get current attendance data for audit log and permission check
    const currentAttendance = await prisma.attendance.findUnique({
      where: { id },
      include: {
        student: true,
        teacher: true,
        class: true,
      },
    });

    if (!currentAttendance) {
      return NextResponse.json({ error: 'Attendance record not found' }, { status: 404 });
    }

    // If user is a teacher, verify they can update this attendance record
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (!teacher || teacher.id !== currentAttendance.teacherId) {
        return NextResponse.json({ error: 'You can only update attendance for your own classes' }, { status: 403 });
      }
    }

    const attendance = await prisma.attendance.update({
      where: { id },
      data: {
        status: data.status,
        notes: data.notes,
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'UPDATE',
      'Attendance',
      attendance.id,
      currentAttendance,
      attendance,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(attendance);
  } catch (error) {
    console.error('Update attendance error:', error);
    return NextResponse.json({ error: 'Failed to update attendance record' }, { status: 500 });
  }
}
