import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { AuthService } from './lib/auth';

// Define protected routes and their required roles
const protectedRoutes = {
  '/': ['ADMIN', 'RECEPTION'], // Dashboard - only admin and reception
  '/class-management/teachers': ['ADMIN'], // Teachers management - admin only
  '/class-management/payments': ['ADMIN'], // Payments - admin only
  '/class-management/classes': ['ADMIN', 'RECEPTION', 'TEACHER'], // Classes - all staff
  '/class-management/students': ['ADMIN', 'RECEPTION'], // Students - admin and reception
  '/class-management/cabinets': ['ADMIN', 'RECEPTION'], // Cabinets - admin and reception
  '/class-management/timetable': ['ADMIN', 'RECEPTION', 'TEACHER'], // Timetable - all staff
  '/api/': ['ADMIN', 'RECEPTION', 'TEACHER'], // API routes - all staff (will be refined per endpoint)
};

// Public routes that don't require authentication
const publicRoutes = [
  '/authentication/login',
  '/authentication/register',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Get token from Authorization header or cookies
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || request.cookies.get('accessToken')?.value;

  if (!token) {
    // Redirect to login for web pages
    if (!pathname.startsWith('/api/')) {
      return NextResponse.redirect(new URL('/authentication/login', request.url));
    }
    // Return 401 for API routes
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Verify token
  const payload = AuthService.verifyToken(token);
  if (!payload) {
    // Clear invalid token and redirect
    const response = pathname.startsWith('/api/')
      ? NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      : NextResponse.redirect(new URL('/authentication/login', request.url));
    
    response.cookies.delete('accessToken');
    return response;
  }

  // Check route permissions
  const requiredRoles = getRequiredRoles(pathname);
  if (requiredRoles.length > 0 && !requiredRoles.includes(payload.role)) {
    // Access denied
    if (pathname.startsWith('/api/')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }
    
    // Redirect to appropriate page based on role
    const redirectUrl = getRedirectUrlForRole(payload.role);
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // Add user info to request headers for API routes
  const response = NextResponse.next();
  response.headers.set('x-user-id', payload.userId);
  response.headers.set('x-user-email', payload.email);
  response.headers.set('x-user-role', payload.role);
  response.headers.set('x-user-name', payload.name);

  return response;
}

function getRequiredRoles(pathname: string): string[] {
  // Check exact matches first
  for (const [route, roles] of Object.entries(protectedRoutes)) {
    if (pathname === route || (route.endsWith('/') && pathname.startsWith(route))) {
      return roles;
    }
  }

  // Check if it's an API route
  if (pathname.startsWith('/api/')) {
    // Default API access for authenticated users
    return ['ADMIN', 'RECEPTION', 'TEACHER'];
  }

  // Default: require authentication but no specific role
  return [];
}

function getRedirectUrlForRole(role: string): string {
  switch (role) {
    case 'ADMIN':
      return '/'; // Dashboard
    case 'RECEPTION':
      return '/class-management/classes'; // Classes page
    case 'TEACHER':
      return '/class-management/classes'; // Classes page
    case 'STUDENT':
      return '/student/dashboard'; // Student dashboard (to be implemented)
    default:
      return '/authentication/login';
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
