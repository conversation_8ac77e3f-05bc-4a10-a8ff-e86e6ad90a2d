/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(DashboardLayout)/page";
exports.ids = ["app/(DashboardLayout)/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "@prisma/client?5f3f":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(DashboardLayout)%2Fpage&page=%2F(DashboardLayout)%2Fpage&appPaths=%2F(DashboardLayout)%2Fpage&pagePath=private-next-app-dir%2F(DashboardLayout)%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(DashboardLayout)%2Fpage&page=%2F(DashboardLayout)%2Fpage&appPaths=%2F(DashboardLayout)%2Fpage&pagePath=private-next-app-dir%2F(DashboardLayout)%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(DashboardLayout)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(DashboardLayout)/page.tsx */ \"(rsc)/./src/app/(DashboardLayout)/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(DashboardLayout)/layout.tsx */ \"(rsc)/./src/app/(DashboardLayout)/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(DashboardLayout)/loading.tsx */ \"(rsc)/./src/app/(DashboardLayout)/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(DashboardLayout)/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(DashboardLayout)/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(DashboardLayout)%2Fpage&page=%2F(DashboardLayout)%2Fpage&appPaths=%2F(DashboardLayout)%2Fpage&pagePath=private-next-app-dir%2F(DashboardLayout)%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(DashboardLayout)/layout.tsx */ \"(ssr)/./src/app/(DashboardLayout)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlcyU1QyU1Q0ltcHJvdmVkLUNSTSU1QyU1Q3BhY2thZ2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoRGFzaGJvYXJkTGF5b3V0KSU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUEwSSIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybml6ZS1uZXh0LWZyZWUvPzIzZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERlc2t0b3BcXFxcY29kZXNcXFxcSW1wcm92ZWQtQ1JNXFxcXHBhY2thZ2VcXFxcc3JjXFxcXGFwcFxcXFwoRGFzaGJvYXJkTGF5b3V0KVxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(DashboardLayout)/page.tsx */ \"(ssr)/./src/app/(DashboardLayout)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlcyU1QyU1Q0ltcHJvdmVkLUNSTSU1QyU1Q3BhY2thZ2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoRGFzaGJvYXJkTGF5b3V0KSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBd0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLz84ZTI5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXEltcHJvdmVkLUNSTVxcXFxwYWNrYWdlXFxcXHNyY1xcXFxhcHBcXFxcKERhc2hib2FyZExheW91dClcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5C(DashboardLayout)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlcyU1QyU1Q0ltcHJvdmVkLUNSTSU1QyU1Q3BhY2thZ2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBdUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLz9lOTA0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXEltcHJvdmVkLUNSTVxcXFxwYWNrYWdlXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/(DashboardLayout)/components/container/PageContainer.tsx ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_helmet_async__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-helmet-async */ \"(ssr)/./node_modules/react-helmet-async/lib/index.module.js\");\n// import { Helmet } from 'react-helmet';\n\n\nconst PageContainer = ({ title, description, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_helmet_async__WEBPACK_IMPORTED_MODULE_1__.HelmetProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_helmet_async__WEBPACK_IMPORTED_MODULE_1__.Helmet, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhEYXNoYm9hcmRMYXlvdXQpL2NvbXBvbmVudHMvY29udGFpbmVyL1BhZ2VDb250YWluZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx5Q0FBeUM7O0FBQ21CO0FBUzVELE1BQU1FLGdCQUFnQixDQUFDLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxRQUFRLEVBQVMsaUJBQzVELDhEQUFDSiw4REFBY0E7a0JBQ2IsNEVBQUNLOzs4QkFDQyw4REFBQ04sc0RBQU1BOztzQ0FDTCw4REFBQ0c7c0NBQU9BOzs7Ozs7c0NBQ1IsOERBQUNJOzRCQUFLQyxNQUFLOzRCQUFjQyxTQUFTTDs7Ozs7Ozs7Ozs7O2dCQUVuQ0M7Ozs7Ozs7Ozs7OztBQUtQLGlFQUFlSCxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9hcHAvKERhc2hib2FyZExheW91dCkvY29tcG9uZW50cy9jb250YWluZXIvUGFnZUNvbnRhaW5lci50c3g/YTRlYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpbXBvcnQgeyBIZWxtZXQgfSBmcm9tICdyZWFjdC1oZWxtZXQnO1xyXG5pbXBvcnQgeyBIZWxtZXQsIEhlbG1ldFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtaGVsbWV0LWFzeW5jJztcclxuXHJcblxyXG50eXBlIFByb3BzID0ge1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIGNoaWxkcmVuOiBKU1guRWxlbWVudCB8IEpTWC5FbGVtZW50W107XHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbn07XHJcblxyXG5jb25zdCBQYWdlQ29udGFpbmVyID0gKHsgdGl0bGUsIGRlc2NyaXB0aW9uLCBjaGlsZHJlbiB9OiBQcm9wcykgPT4gKFxyXG4gIDxIZWxtZXRQcm92aWRlcj5cclxuICAgIDxkaXY+XHJcbiAgICAgIDxIZWxtZXQ+XHJcbiAgICAgICAgPHRpdGxlPnt0aXRsZX08L3RpdGxlPlxyXG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e2Rlc2NyaXB0aW9ufSAvPlxyXG4gICAgICA8L0hlbG1ldD5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9kaXY+XHJcbiAgPC9IZWxtZXRQcm92aWRlcj5cclxuKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFBhZ2VDb250YWluZXI7XHJcbiJdLCJuYW1lcyI6WyJIZWxtZXQiLCJIZWxtZXRQcm92aWRlciIsIlBhZ2VDb250YWluZXIiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY2hpbGRyZW4iLCJkaXYiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout.tsx":
/*!**********************************************!*\
  !*** ./src/app/(DashboardLayout)/layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/header/Header */ \"(ssr)/./src/app/(DashboardLayout)/layout/header/Header.tsx\");\n/* harmony import */ var _app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/sidebar/Sidebar */ \"(ssr)/./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst MainWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\")(()=>({\n        display: \"flex\",\n        minHeight: \"100vh\",\n        width: \"100%\"\n    }));\nconst PageWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\", {\n    shouldForwardProp: (prop)=>prop !== \"isSidebarOpen\"\n})(({ theme, isSidebarOpen })=>({\n        display: \"flex\",\n        flexGrow: 1,\n        paddingBottom: \"60px\",\n        flexDirection: \"column\",\n        zIndex: 1,\n        backgroundColor: \"transparent\",\n        marginLeft: \"0px\",\n        transition: \"all 0.2s ease-in-out\",\n        [theme.breakpoints.up(\"lg\")]: {\n            marginLeft: isSidebarOpen ? \"270px\" : \"0px\"\n        }\n    }));\nfunction RootLayout({ children }) {\n    const [isSidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobileSidebarOpen, setMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!isSidebarOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainWrapper, {\n            className: \"mainwrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    isSidebarOpen: isSidebarOpen,\n                    isMobileSidebarOpen: isMobileSidebarOpen,\n                    onSidebarClose: ()=>setMobileSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageWrapper, {\n                    className: \"page-wrapper\",\n                    isSidebarOpen: isSidebarOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            toggleMobileSidebar: ()=>setMobileSidebarOpen(true),\n                            toggleSidebar: toggleSidebar,\n                            isSidebarOpen: isSidebarOpen\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                paddingTop: \"20px\",\n                                maxWidth: \"1200px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    minHeight: \"calc(100vh - 170px)\"\n                                },\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/header/Header.tsx":
/*!************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/header/Header.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Badge/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Badge,Box,IconButton,Stack,Toolbar,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Profile */ \"(ssr)/./src/app/(DashboardLayout)/layout/header/Profile.tsx\");\n/* harmony import */ var _barrel_optimize_names_IconBellRinging_IconMenu_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconBellRinging,IconMenu!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMenu.js\");\n/* harmony import */ var _barrel_optimize_names_IconBellRinging_IconMenu_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconBellRinging,IconMenu!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBellRinging.js\");\n\n\n\n\n// components\n\n\nconst Header = ({ toggleMobileSidebar, toggleSidebar, isSidebarOpen })=>{\n    const lgUp = (0,_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((theme)=>theme.breakpoints.up(\"lg\"));\n    const AppBarStyled = (0,_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(({ theme })=>({\n            boxShadow: \"none\",\n            background: theme.palette.background.paper,\n            justifyContent: \"center\",\n            backdropFilter: \"blur(4px)\",\n            [theme.breakpoints.up(\"lg\")]: {\n                minHeight: \"70px\"\n            }\n        }));\n    const ToolbarStyled = (0,_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(({ theme })=>({\n            width: \"100%\",\n            color: theme.palette.text.secondary\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppBarStyled, {\n        position: \"sticky\",\n        color: \"default\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolbarStyled, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    color: \"inherit\",\n                    \"aria-label\": \"menu\",\n                    onClick: toggleMobileSidebar,\n                    sx: {\n                        display: {\n                            lg: \"none\",\n                            xs: \"inline\"\n                        }\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBellRinging_IconMenu_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        width: \"20\",\n                        height: \"20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                lgUp && toggleSidebar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    color: \"inherit\",\n                    \"aria-label\": \"toggle sidebar\",\n                    onClick: toggleSidebar,\n                    sx: {\n                        mr: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBellRinging_IconMenu_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        width: \"20\",\n                        height: \"20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: \"large\",\n                    \"aria-label\": \"show notifications\",\n                    color: \"inherit\",\n                    \"aria-controls\": \"msgs-menu\",\n                    \"aria-haspopup\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"dot\",\n                        color: \"primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBellRinging_IconMenu_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: \"21\",\n                            stroke: \"1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    flexGrow: 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Badge_Box_IconButton_Stack_Toolbar_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    spacing: 1,\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Header.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nHeader.propTypes = {\n    sx: (prop_types__WEBPACK_IMPORTED_MODULE_13___default().object),\n    toggleMobileSidebar: (prop_types__WEBPACK_IMPORTED_MODULE_13___default().func).isRequired,\n    toggleSidebar: (prop_types__WEBPACK_IMPORTED_MODULE_13___default().func),\n    isSidebarOpen: (prop_types__WEBPACK_IMPORTED_MODULE_13___default().bool)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/header/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/header/Profile.tsx":
/*!*************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/header/Profile.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,IconButton,ListItemIcon,ListItemText,Menu,MenuItem!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconListCheck,IconMail,IconUser!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconUser.js\");\n/* harmony import */ var _barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconListCheck,IconMail,IconUser!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMail.js\");\n/* harmony import */ var _barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconListCheck,IconMail,IconUser!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconListCheck.js\");\n\n\n\n\n\nconst Profile = ()=>{\n    const [anchorEl2, setAnchorEl2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleClick2 = (event)=>{\n        setAnchorEl2(event.currentTarget);\n    };\n    const handleClose2 = ()=>{\n        setAnchorEl2(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"large\",\n                \"aria-label\": \"show 11 new notifications\",\n                color: \"inherit\",\n                \"aria-controls\": \"msgs-menu\",\n                \"aria-haspopup\": \"true\",\n                sx: {\n                    ...typeof anchorEl2 === \"object\" && {\n                        color: \"primary.main\"\n                    }\n                },\n                onClick: handleClick2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    src: \"/images/profile/user-1.jpg\",\n                    alt: \"image\",\n                    sx: {\n                        width: 35,\n                        height: 35\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                id: \"msgs-menu\",\n                anchorEl: anchorEl2,\n                keepMounted: true,\n                open: Boolean(anchorEl2),\n                onClose: handleClose2,\n                anchorOrigin: {\n                    horizontal: \"right\",\n                    vertical: \"bottom\"\n                },\n                transformOrigin: {\n                    horizontal: \"right\",\n                    vertical: \"top\"\n                },\n                sx: {\n                    \"& .MuiMenu-paper\": {\n                        width: \"200px\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    width: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: \"My Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    width: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: \"My Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconListCheck_IconMail_IconUser_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    width: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: \"My Tasks\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        mt: 1,\n                        py: 1,\n                        px: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            href: \"/authentication/login\",\n                            variant: \"outlined\",\n                            color: \"primary\",\n                            component: next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                            fullWidth: true,\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\header\\\\Profile.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/header/Profile.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/sidebar/MenuItems.tsx":
/*!****************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/sidebar/MenuItems.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconLayoutDashboard.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSchool.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconUsers.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChalkboard.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconDoor.js\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconCash,IconChalkboard,IconDoor,IconLayoutDashboard,IconSchool,IconUsers!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCash.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/uniqueId */ \"(ssr)/./node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst Menuitems = [\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Главная\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        href: \"/\"\n    },\n    {\n        navlabel: true,\n        subheader: \"Управление классами\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Группы\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        href: \"/class-management/classes\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Студенты\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: \"/class-management/students\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Учителя\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: \"/class-management/teachers\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Расписание\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: \"/class-management/timetable\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Кабинеты\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: \"/class-management/cabinets\"\n    },\n    {\n        id: lodash_uniqueId__WEBPACK_IMPORTED_MODULE_0___default()(),\n        title: \"Оплата\",\n        icon: _barrel_optimize_names_IconCalendar_IconCash_IconChalkboard_IconDoor_IconLayoutDashboard_IconSchool_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/class-management/payments\"\n    }\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Menuitems);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/sidebar/MenuItems.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavGroup/NavGroup.tsx":
/*!************************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/sidebar/NavGroup/NavGroup.tsx ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ListSubheader_styled_mui_material__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ListSubheader,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_ListSubheader_styled_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ListSubheader,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListSubheader/ListSubheader.js\");\n\n\n// mui imports\n\nconst NavGroup = ({ item })=>{\n    const ListSubheaderStyle = (0,_barrel_optimize_names_ListSubheader_styled_mui_material__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListSubheader_styled_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            disableSticky: true,\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavGroup\\\\NavGroup.tsx\",\n            lineNumber: 15,\n            columnNumber: 61\n        }, undefined))(({ theme })=>({\n            ...theme.typography.overline,\n            fontWeight: \"700\",\n            marginTop: theme.spacing(3),\n            marginBottom: theme.spacing(0),\n            color: theme.palette.text.primary,\n            lineHeight: \"26px\",\n            padding: \"3px 12px\"\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListSubheaderStyle, {\n        children: item.subheader\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavGroup\\\\NavGroup.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\nNavGroup.propTypes = {\n    item: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavGroup);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavGroup/NavGroup.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavItem/index.tsx":
/*!********************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/sidebar/NavItem/index.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=List,ListItem,ListItemButton,ListItemIcon,ListItemText,styled,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth */ \"(ssr)/./src/utils/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst NavItem = ({ item, level, pathDirect, onClick })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const Icon = item.icon;\n    const theme = (0,_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const itemIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n        stroke: 1.5,\n        size: \"1.3rem\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n        lineNumber: 39,\n        columnNumber: 20\n    }, undefined);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only hide items for reception user if they are restricted\n        const shouldHide = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.isReceptionUser)() && (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.isRestrictedPage)(item.href);\n        setIsVisible(!shouldHide);\n    }, [\n        item.href\n    ]);\n    if (!isVisible) return null;\n    const ListItemStyled = (0,_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(()=>({\n            padding: 0,\n            \".MuiButtonBase-root\": {\n                whiteSpace: \"nowrap\",\n                marginBottom: \"2px\",\n                padding: \"8px 10px\",\n                borderRadius: \"8px\",\n                backgroundColor: level > 1 ? \"transparent !important\" : \"inherit\",\n                color: theme.palette.text.secondary,\n                paddingLeft: \"10px\",\n                \"&:hover\": {\n                    backgroundColor: theme.palette.primary.light,\n                    color: theme.palette.primary.main\n                },\n                \"&.Mui-selected\": {\n                    color: \"white\",\n                    backgroundColor: theme.palette.primary.main,\n                    \"&:hover\": {\n                        backgroundColor: theme.palette.primary.main,\n                        color: \"white\"\n                    }\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        component: \"div\",\n        disablePadding: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListItemStyled, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                component: next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                href: item.href,\n                disabled: item.disabled,\n                selected: pathDirect === item.href,\n                target: item.external ? \"_blank\" : \"\",\n                onClick: onClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            minWidth: \"36px\",\n                            p: \"3px 0\",\n                            color: \"inherit\"\n                        },\n                        children: itemIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_List_ListItem_ListItemButton_ListItemIcon_ListItemText_styled_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: item.title\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined)\n    }, item.id, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\NavItem\\\\index.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavItem/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Drawer,Typography,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Drawer,Typography,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Drawer,Typography,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Drawer,Typography,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Drawer,Typography,styled,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _SidebarItems__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SidebarItems */ \"(ssr)/./src/app/(DashboardLayout)/layout/sidebar/SidebarItems.tsx\");\n\n\n\nconst StyledDrawer = (0,_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(({ theme })=>({\n        \"& .MuiDrawer-paper\": {\n            width: \"270px\",\n            border: \"none\",\n            transition: theme.transitions.create([\n                \"width\",\n                \"transform\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.standard\n            }),\n            overflowX: \"hidden\",\n            \"&::-webkit-scrollbar\": {\n                width: \"7px\"\n            },\n            \"&::-webkit-scrollbar-thumb\": {\n                backgroundColor: \"#eff2f7\",\n                borderRadius: \"15px\"\n            }\n        }\n    }));\nconst LogoWrapper = (0,_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n    padding: \"20px 20px 0px 20px\",\n    textAlign: \"center\"\n});\nconst MSidebar = ({ isMobileSidebarOpen, onSidebarClose, isSidebarOpen })=>{\n    const lgUp = (0,_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((theme)=>theme.breakpoints.up(\"lg\"));\n    const sidebarContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoWrapper, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"h5\",\n                    sx: {\n                        fontWeight: \"bold\",\n                        color: \"primary.main\"\n                    },\n                    children: \"Innovative Centre\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Drawer_Typography_styled_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    px: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SidebarItems__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n    if (lgUp) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledDrawer, {\n            variant: \"permanent\",\n            open: isSidebarOpen,\n            anchor: \"left\",\n            sx: {\n                \"& .MuiDrawer-paper\": {\n                    transform: isSidebarOpen ? \"none\" : \"translateX(-270px)\"\n                }\n            },\n            children: sidebarContent\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledDrawer, {\n        anchor: \"left\",\n        open: isMobileSidebarOpen,\n        onClose: onSidebarClose,\n        variant: \"temporary\",\n        sx: {\n            \"& .MuiDrawer-paper\": {\n                boxShadow: (theme)=>theme.shadows[8]\n            }\n        },\n        children: sidebarContent\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\Sidebar.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/sidebar/SidebarItems.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/sidebar/SidebarItems.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MenuItems__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MenuItems */ \"(ssr)/./src/app/(DashboardLayout)/layout/sidebar/MenuItems.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Box_List_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,List!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_List_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,List!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _NavItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NavItem */ \"(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavItem/index.tsx\");\n/* harmony import */ var _NavGroup_NavGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NavGroup/NavGroup */ \"(ssr)/./src/app/(DashboardLayout)/layout/sidebar/NavGroup/NavGroup.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?5f3f\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\n\nconst SidebarItems = ({ toggleMobileSidebar })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const pathDirect = pathname;\n    const { user, hasPermission } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const filteredMenuItems = _MenuItems__WEBPACK_IMPORTED_MODULE_2__[\"default\"].filter((item)=>{\n        // Filter menu items based on user role\n        if (item.title === \"Главная\") {\n            return hasPermission(_prisma_client__WEBPACK_IMPORTED_MODULE_7__.UserRole.RECEPTION); // Admin and Reception can access dashboard\n        }\n        if (item.title === \"Учителя\") {\n            return hasPermission(_prisma_client__WEBPACK_IMPORTED_MODULE_7__.UserRole.ADMIN); // Only Admin can access teachers\n        }\n        if (item.title === \"Оплата\") {\n            return hasPermission(_prisma_client__WEBPACK_IMPORTED_MODULE_7__.UserRole.ADMIN); // Only Admin can access payments\n        }\n        if (item.title === \"Студенты\") {\n            return hasPermission(_prisma_client__WEBPACK_IMPORTED_MODULE_7__.UserRole.RECEPTION); // Admin and Reception can access students\n        }\n        // All other items are accessible to authenticated users\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_List_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            px: 3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_List_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                pt: 0\n            },\n            className: \"sidebarNav\",\n            component: \"div\",\n            children: filteredMenuItems.map((item)=>{\n                // {/********SubHeader**********/}\n                if (item.subheader) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavGroup_NavGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: item\n                    }, item.subheader, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\SidebarItems.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 20\n                    }, undefined);\n                // {/********If Sub Menu**********/}\n                /* eslint no-else-return: \"off\" */ } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        item: item,\n                        pathDirect: pathDirect,\n                        onClick: toggleMobileSidebar\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\SidebarItems.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, undefined);\n                }\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\SidebarItems.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\sidebar\\\\SidebarItems.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarItems);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/sidebar/SidebarItems.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/page.tsx":
/*!********************************************!*\
  !*** ./src/app/(DashboardLayout)/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _app_DashboardLayout_components_container_PageContainer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/(DashboardLayout)/components/container/PageContainer */ \"(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_usePayments__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePayments */ \"(ssr)/./src/hooks/usePayments.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Loading Component\nconst LoadingCard = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3,\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"150px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n// Error Component\nconst ErrorCard = ({ message })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            severity: \"error\",\n            children: message\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined);\n// Stats Card Component\nconst StatsCard = ({ title, value, color })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"subtitle2\",\n                    color: \"textSecondary\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h4\",\n                    color: color,\n                    children: value\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n// Stats Overview Component\nconst StatsOverview = ()=>{\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { getStudentPaymentStatus } = (0,_hooks_usePayments__WEBPACK_IMPORTED_MODULE_3__.usePayments)();\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                const [studentsRes, teachersRes, classesRes] = await Promise.all([\n                    fetch(\"/api/students\"),\n                    fetch(\"/api/teachers\"),\n                    fetch(\"/api/classes\")\n                ]);\n                if (!studentsRes.ok || !teachersRes.ok || !classesRes.ok) {\n                    throw new Error(\"Failed to fetch data\");\n                }\n                const [studentsData, teachersData, classesData] = await Promise.all([\n                    studentsRes.json(),\n                    teachersRes.json(),\n                    classesRes.json()\n                ]);\n                setStudents(studentsData);\n                setTeachers(teachersData);\n                setClasses(classesData);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            container: true,\n            spacing: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorCard, {\n                    message: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            container: true,\n            spacing: 3,\n            children: [\n                1,\n                2,\n                3\n            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    item: true,\n                    xs: 12,\n                    sm: 4,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, undefined)\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, undefined);\n    }\n    const activeStudents = students.filter((s)=>getStudentPaymentStatus(s.id) === \"paid\").length;\n    const activeTeachers = teachers.filter((t)=>t.status === \"active\").length;\n    const totalClasses = classes.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        container: true,\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                    title: \"Active Students\",\n                    value: activeStudents,\n                    color: \"primary.main\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                    title: \"Active Teachers\",\n                    value: activeTeachers,\n                    color: \"success.main\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                    title: \"Total Classes\",\n                    value: totalClasses,\n                    color: \"warning.main\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n// Class Levels Component\nconst ClassLevels = ()=>{\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchClasses = async ()=>{\n            try {\n                const response = await fetch(\"/api/classes\");\n                if (!response.ok) throw new Error(\"Failed to fetch classes\");\n                const data = await response.json();\n                setClasses(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchClasses();\n    }, []);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorCard, {\n        message: error\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 21\n    }, undefined);\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 23\n    }, undefined);\n    const levels = classes.reduce((acc, cls)=>{\n        acc[cls.level] = (acc[cls.level] || 0) + 1;\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3,\n            minHeight: \"240px\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                mb: 2,\n                children: \"Class Distribution\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                spacing: 2,\n                sx: {\n                    flex: 1\n                },\n                children: Object.keys(levels).length > 0 ? Object.entries(levels).map(([level, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            py: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"subtitle1\",\n                                children: level\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"h6\",\n                                color: \"primary\",\n                                children: [\n                                    count,\n                                    \" classes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, level, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        flex: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"textSecondary\",\n                        children: \"No classes available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n// Recent Payments Component\nconst RecentPayments = ()=>{\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { getStudentPaymentStatus } = (0,_hooks_usePayments__WEBPACK_IMPORTED_MODULE_3__.usePayments)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchPayments = async ()=>{\n            try {\n                const response = await fetch(\"/api/payments\");\n                if (!response.ok) throw new Error(\"Failed to fetch payments\");\n                const data = await response.json();\n                setPayments(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPayments();\n    }, []);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorCard, {\n        message: error\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 21\n    }, undefined);\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 215,\n        columnNumber: 23\n    }, undefined);\n    const formatCurrency = (amount)=>{\n        return amount.toLocaleString(\"uz-UZ\", {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }) + \" UZS\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3,\n            minHeight: \"240px\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                mb: 2,\n                children: \"Recent Payments\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                spacing: 2,\n                sx: {\n                    flex: 1\n                },\n                children: payments.length > 0 ? payments.slice(0, 5).map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            py: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"subtitle2\",\n                                        children: payment.student.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"textSecondary\",\n                                        children: new Date(payment.date).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"subtitle1\",\n                                color: getStudentPaymentStatus(payment.student.id) === \"paid\" ? \"success.main\" : \"error.main\",\n                                children: formatCurrency(payment.amount)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, payment.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        flex: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"textSecondary\",\n                        children: \"No recent payments\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n// Cabinet Status Component\nconst CabinetStatus = ()=>{\n    const [cabinets, setCabinets] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchCabinets = async ()=>{\n            try {\n                const response = await fetch(\"/api/cabinets\");\n                if (!response.ok) throw new Error(\"Failed to fetch cabinets\");\n                const data = await response.json();\n                setCabinets(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCabinets();\n    }, []);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorCard, {\n        message: error\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 286,\n        columnNumber: 21\n    }, undefined);\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 287,\n        columnNumber: 23\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3,\n            minHeight: \"240px\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                mb: 2,\n                children: \"Cabinet Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                spacing: 2,\n                sx: {\n                    flex: 1,\n                    overflow: \"auto\",\n                    maxHeight: \"calc(240px - 64px)\",\n                    \"&::-webkit-scrollbar\": {\n                        width: \"6px\"\n                    },\n                    \"&::-webkit-scrollbar-thumb\": {\n                        backgroundColor: \"rgba(0,0,0,0.2)\",\n                        borderRadius: \"3px\"\n                    }\n                },\n                children: cabinets.length > 0 ? cabinets.map((cabinet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            py: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"subtitle2\",\n                                        children: cabinet.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"textSecondary\",\n                                        children: [\n                                            \"Capacity: \",\n                                            cabinet.capacity\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"subtitle2\",\n                                sx: {\n                                    color: cabinet.status === \"available\" ? \"success.main\" : cabinet.status === \"occupied\" ? \"warning.main\" : \"error.main\"\n                                },\n                                children: cabinet.status.charAt(0).toUpperCase() + cabinet.status.slice(1)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, cabinet.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        flex: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"textSecondary\",\n                        children: \"No cabinets available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, undefined);\n};\nconst Dashboard = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_container_PageContainer__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Dashboard\",\n        description: \"Centre Overview\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 33\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsOverview, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 33\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClassLevels, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 33\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecentPayments, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingCard, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 33\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CabinetStatus, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/theme/DefaultColors */ \"(ssr)/./src/utils/theme/DefaultColors.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction LayoutContent({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isAuthPage = pathname?.startsWith(\"/authentication/\");\n    if (isAuthPage) {\n        // Don't wrap authentication pages with AuthProvider\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Wrap other pages with AuthProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                theme: _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__.baselightTheme,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContent, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?5f3f\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Define restricted routes for different roles\nconst restrictedRoutes = {\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\"\n    ],\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.TEACHER]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\",\n        \"/class-management/students\"\n    ],\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.STUDENT]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\",\n        \"/class-management/students\",\n        \"/class-management/classes\",\n        \"/class-management/cabinets\",\n        \"/class-management/timetable\"\n    ]\n};\nfunction ProtectedRoute({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) return; // Wait for auth check to complete\n        if (!isAuthenticated && pathname !== \"/authentication/login\") {\n            router.push(\"/authentication/login\");\n            return;\n        }\n        // Check if user has access to current route\n        if (isAuthenticated && user) {\n            const userRestrictedRoutes = restrictedRoutes[user.role] || [];\n            const isRestricted = userRestrictedRoutes.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n            if (isRestricted) {\n                // Redirect to appropriate page based on role\n                const redirectUrl = getRedirectUrlForRole(user.role);\n                router.push(redirectUrl);\n            }\n        }\n    }, [\n        pathname,\n        router,\n        isAuthenticated,\n        user,\n        isLoading\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!isAuthenticated && pathname !== \"/authentication/login\") {\n        return null;\n    }\n    // Don't render restricted pages\n    if (isAuthenticated && user) {\n        const userRestrictedRoutes = restrictedRoutes[user.role] || [];\n        const isRestricted = userRestrictedRoutes.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n        if (isRestricted) {\n            return null;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction getRedirectUrlForRole(role) {\n    switch(role){\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.ADMIN:\n            return \"/\"; // Dashboard\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION:\n            return \"/class-management/classes\"; // Classes page\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.TEACHER:\n            return \"/class-management/classes\"; // Classes page\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.STUDENT:\n            return \"/student/dashboard\"; // Student dashboard (to be implemented)\n        default:\n            return \"/authentication/login\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?5f3f\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated\n    const isAuthenticated = !!user;\n    // Role hierarchy for permission checking\n    const roleHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.STUDENT]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.TEACHER]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.RECEPTION]: 2,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN]: 3\n    };\n    // Check if user has required permission\n    const hasPermission = (requiredRole)=>{\n        if (!user) return false;\n        return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n    };\n    // Login function\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setUser(data.user);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error occurred\"\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            // Redirect to login page using Next.js navigation\n            if (false) {}\n        }\n    };\n    // Refresh token function\n    const refreshToken = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.user);\n                    return true;\n                }\n            }\n            // If refresh fails, logout user\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Get current user on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getCurrentUser = async ()=>{\n            try {\n                const response = await fetch(\"/api/auth/me\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    }\n                } else if (response.status === 401) {\n                    // Try to refresh token\n                    const refreshSuccess = await refreshToken();\n                    if (!refreshSuccess) {\n                        // Don't redirect here, let ProtectedRoute handle it\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Get current user error:\", error);\n                setUser(null);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getCurrentUser();\n    }, []);\n    // Auto-refresh token before expiry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(async ()=>{\n            await refreshToken();\n        }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/usePayments.ts":
/*!**********************************!*\
  !*** ./src/hooks/usePayments.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePayments: () => (/* binding */ usePayments)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Event bus for payment updates\nconst paymentEventBus = {\n    listeners: new Set(),\n    subscribe (listener) {\n        this.listeners.add(listener);\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    },\n    publish () {\n        this.listeners.forEach((listener)=>listener());\n    }\n};\nconst usePayments = ()=>{\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchPayments = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/payments\");\n            if (!response.ok) throw new Error(\"Failed to fetch payments\");\n            const data = await response.json();\n            setPayments(data);\n            setError(null);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchPayments();\n        // Subscribe to payment updates\n        const unsubscribe = paymentEventBus.subscribe(fetchPayments);\n        return unsubscribe;\n    }, [\n        fetchPayments\n    ]);\n    const updatePayment = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (paymentData)=>{\n        try {\n            const response = await fetch(`/api/payments?id=${paymentData.id}`, {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(paymentData)\n            });\n            if (!response.ok) throw new Error(\"Failed to update payment\");\n            const updatedPayment = await response.json();\n            setPayments((prevPayments)=>prevPayments.map((payment)=>payment.id === updatedPayment.id ? updatedPayment : payment));\n            // Notify all subscribers about the update\n            paymentEventBus.publish();\n            return updatedPayment;\n        } catch (error) {\n            throw error;\n        }\n    }, []);\n    const createPayment = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (paymentData)=>{\n        try {\n            const response = await fetch(\"/api/payments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(paymentData)\n            });\n            if (!response.ok) throw new Error(\"Failed to create payment\");\n            const newPayment = await response.json();\n            setPayments((prevPayments)=>[\n                    newPayment,\n                    ...prevPayments\n                ]);\n            // Notify all subscribers about the update\n            paymentEventBus.publish();\n            return newPayment;\n        } catch (error) {\n            throw error;\n        }\n    }, []);\n    const deletePayment = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (paymentId)=>{\n        try {\n            const response = await fetch(`/api/payments?id=${paymentId}`, {\n                method: \"DELETE\"\n            });\n            if (!response.ok) throw new Error(\"Failed to delete payment\");\n            setPayments((prevPayments)=>prevPayments.filter((payment)=>payment.id !== paymentId));\n            // Notify all subscribers about the update\n            paymentEventBus.publish();\n        } catch (error) {\n            throw error;\n        }\n    }, []);\n    const getStudentPaymentStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((studentId)=>{\n        const studentPayments = payments.filter((p)=>p.studentId === studentId);\n        if (studentPayments.length === 0) return \"unpaid\";\n        const hasUnpaidPayments = studentPayments.some((p)=>p.status === \"unpaid\");\n        return hasUnpaidPayments ? \"unpaid\" : \"paid\";\n    }, [\n        payments\n    ]);\n    return {\n        payments,\n        loading,\n        error,\n        updatePayment,\n        createPayment,\n        deletePayment,\n        getStudentPaymentStatus,\n        refreshPayments: fetchPayments\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlUGF5bWVudHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlEO0FBR3pELGdDQUFnQztBQUNoQyxNQUFNRyxrQkFBa0I7SUFDdEJDLFdBQVcsSUFBSUM7SUFDZkMsV0FBVUMsUUFBb0I7UUFDNUIsSUFBSSxDQUFDSCxTQUFTLENBQUNJLEdBQUcsQ0FBQ0Q7UUFDbkIsT0FBTztZQUNMLElBQUksQ0FBQ0gsU0FBUyxDQUFDSyxNQUFNLENBQUNGO1FBQ3hCO0lBQ0Y7SUFDQUc7UUFDRSxJQUFJLENBQUNOLFNBQVMsQ0FBQ08sT0FBTyxDQUFDSixDQUFBQSxXQUFZQTtJQUNyQztBQUNGO0FBRU8sTUFBTUssY0FBYztJQUN6QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQXVCLEVBQUU7SUFDakUsTUFBTSxDQUFDZSxTQUFTQyxXQUFXLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQixPQUFPQyxTQUFTLEdBQUdsQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTW1CLGdCQUFnQmpCLGtEQUFXQSxDQUFDO1FBQ2hDLElBQUk7WUFDRmMsV0FBVztZQUNYLE1BQU1JLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJLENBQUNELFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsT0FBTyxNQUFNSixTQUFTSyxJQUFJO1lBQ2hDWCxZQUFZVTtZQUNaTixTQUFTO1FBQ1gsRUFBRSxPQUFPUSxLQUFLO1lBQ1pSLFNBQVNRLGVBQWVILFFBQVFHLElBQUlDLE9BQU8sR0FBRztRQUNoRCxTQUFVO1lBQ1JYLFdBQVc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMZixnREFBU0EsQ0FBQztRQUNSa0I7UUFDQSwrQkFBK0I7UUFDL0IsTUFBTVMsY0FBY3pCLGdCQUFnQkcsU0FBUyxDQUFDYTtRQUM5QyxPQUFPUztJQUNULEdBQUc7UUFBQ1Q7S0FBYztJQUVsQixNQUFNVSxnQkFBZ0IzQixrREFBV0EsQ0FBQyxPQUFPNEI7UUFDdkMsSUFBSTtZQUNGLE1BQU1WLFdBQVcsTUFBTUMsTUFBTSxDQUFDLGlCQUFpQixFQUFFUyxZQUFZQyxFQUFFLENBQUMsQ0FBQyxFQUFFO2dCQUNqRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDTjtZQUN2QjtZQUVBLElBQUksQ0FBQ1YsU0FBU0UsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUNsQyxNQUFNYyxpQkFBaUIsTUFBTWpCLFNBQVNLLElBQUk7WUFFMUNYLFlBQVl3QixDQUFBQSxlQUNWQSxhQUFhQyxHQUFHLENBQUNDLENBQUFBLFVBQ2ZBLFFBQVFULEVBQUUsS0FBS00sZUFBZU4sRUFBRSxHQUFHTSxpQkFBaUJHO1lBSXhELDBDQUEwQztZQUMxQ3JDLGdCQUFnQk8sT0FBTztZQUN2QixPQUFPMkI7UUFDVCxFQUFFLE9BQU9wQixPQUFPO1lBQ2QsTUFBTUE7UUFDUjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU13QixnQkFBZ0J2QyxrREFBV0EsQ0FBQyxPQUFPNEI7UUFDdkMsSUFBSTtZQUNGLE1BQU1WLFdBQVcsTUFBTUMsTUFBTSxpQkFBaUI7Z0JBQzVDVyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNOO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDVixTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDLE1BQU1tQixhQUFhLE1BQU10QixTQUFTSyxJQUFJO1lBRXRDWCxZQUFZd0IsQ0FBQUEsZUFBZ0I7b0JBQUNJO3VCQUFlSjtpQkFBYTtZQUV6RCwwQ0FBMEM7WUFDMUNuQyxnQkFBZ0JPLE9BQU87WUFDdkIsT0FBT2dDO1FBQ1QsRUFBRSxPQUFPekIsT0FBTztZQUNkLE1BQU1BO1FBQ1I7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNMEIsZ0JBQWdCekMsa0RBQVdBLENBQUMsT0FBTzBDO1FBQ3ZDLElBQUk7WUFDRixNQUFNeEIsV0FBVyxNQUFNQyxNQUFNLENBQUMsaUJBQWlCLEVBQUV1QixVQUFVLENBQUMsRUFBRTtnQkFDNURaLFFBQVE7WUFDVjtZQUVBLElBQUksQ0FBQ1osU0FBU0UsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUVsQ1QsWUFBWXdCLENBQUFBLGVBQ1ZBLGFBQWFPLE1BQU0sQ0FBQ0wsQ0FBQUEsVUFBV0EsUUFBUVQsRUFBRSxLQUFLYTtZQUdoRCwwQ0FBMEM7WUFDMUN6QyxnQkFBZ0JPLE9BQU87UUFDekIsRUFBRSxPQUFPTyxPQUFPO1lBQ2QsTUFBTUE7UUFDUjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU02QiwwQkFBMEI1QyxrREFBV0EsQ0FBQyxDQUFDNkM7UUFDM0MsTUFBTUMsa0JBQWtCbkMsU0FBU2dDLE1BQU0sQ0FBQ0ksQ0FBQUEsSUFBS0EsRUFBRUYsU0FBUyxLQUFLQTtRQUM3RCxJQUFJQyxnQkFBZ0JFLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFFekMsTUFBTUMsb0JBQW9CSCxnQkFBZ0JJLElBQUksQ0FBQ0gsQ0FBQUEsSUFBS0EsRUFBRUksTUFBTSxLQUFLO1FBQ2pFLE9BQU9GLG9CQUFvQixXQUFXO0lBQ3hDLEdBQUc7UUFBQ3RDO0tBQVM7SUFFYixPQUFPO1FBQ0xBO1FBQ0FFO1FBQ0FFO1FBQ0FZO1FBQ0FZO1FBQ0FFO1FBQ0FHO1FBQ0FRLGlCQUFpQm5DO0lBQ25CO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybml6ZS1uZXh0LWZyZWUvLi9zcmMvaG9va3MvdXNlUGF5bWVudHMudHM/ZWU3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgUGF5bWVudFdpdGhTdHVkZW50IH0gZnJvbSAnQC90eXBlcy9wYXltZW50JztcclxuXHJcbi8vIEV2ZW50IGJ1cyBmb3IgcGF5bWVudCB1cGRhdGVzXHJcbmNvbnN0IHBheW1lbnRFdmVudEJ1cyA9IHtcclxuICBsaXN0ZW5lcnM6IG5ldyBTZXQ8KCkgPT4gdm9pZD4oKSxcclxuICBzdWJzY3JpYmUobGlzdGVuZXI6ICgpID0+IHZvaWQpIHtcclxuICAgIHRoaXMubGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICB0aGlzLmxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xyXG4gICAgfTtcclxuICB9LFxyXG4gIHB1Ymxpc2goKSB7XHJcbiAgICB0aGlzLmxpc3RlbmVycy5mb3JFYWNoKGxpc3RlbmVyID0+IGxpc3RlbmVyKCkpO1xyXG4gIH0sXHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdXNlUGF5bWVudHMgPSAoKSA9PiB7XHJcbiAgY29uc3QgW3BheW1lbnRzLCBzZXRQYXltZW50c10gPSB1c2VTdGF0ZTxQYXltZW50V2l0aFN0dWRlbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIGNvbnN0IGZldGNoUGF5bWVudHMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3BheW1lbnRzJyk7XHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHBheW1lbnRzJyk7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHNldFBheW1lbnRzKGRhdGEpO1xyXG4gICAgICBzZXRFcnJvcihudWxsKTtcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0FuIGVycm9yIG9jY3VycmVkJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaFBheW1lbnRzKCk7XHJcbiAgICAvLyBTdWJzY3JpYmUgdG8gcGF5bWVudCB1cGRhdGVzXHJcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IHBheW1lbnRFdmVudEJ1cy5zdWJzY3JpYmUoZmV0Y2hQYXltZW50cyk7XHJcbiAgICByZXR1cm4gdW5zdWJzY3JpYmU7XHJcbiAgfSwgW2ZldGNoUGF5bWVudHNdKTtcclxuXHJcbiAgY29uc3QgdXBkYXRlUGF5bWVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXltZW50RGF0YTogUGFydGlhbDxQYXltZW50V2l0aFN0dWRlbnQ+ICYgeyBpZDogc3RyaW5nIH0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcGF5bWVudHM/aWQ9JHtwYXltZW50RGF0YS5pZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwYXltZW50RGF0YSksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHBheW1lbnQnKTtcclxuICAgICAgY29uc3QgdXBkYXRlZFBheW1lbnQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICBzZXRQYXltZW50cyhwcmV2UGF5bWVudHMgPT5cclxuICAgICAgICBwcmV2UGF5bWVudHMubWFwKHBheW1lbnQgPT5cclxuICAgICAgICAgIHBheW1lbnQuaWQgPT09IHVwZGF0ZWRQYXltZW50LmlkID8gdXBkYXRlZFBheW1lbnQgOiBwYXltZW50XHJcbiAgICAgICAgKVxyXG4gICAgICApO1xyXG5cclxuICAgICAgLy8gTm90aWZ5IGFsbCBzdWJzY3JpYmVycyBhYm91dCB0aGUgdXBkYXRlXHJcbiAgICAgIHBheW1lbnRFdmVudEJ1cy5wdWJsaXNoKCk7XHJcbiAgICAgIHJldHVybiB1cGRhdGVkUGF5bWVudDtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgY3JlYXRlUGF5bWVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXltZW50RGF0YTogT21pdDxQYXltZW50V2l0aFN0dWRlbnQsICdpZCcgfCAnc3R1ZGVudCc+ICYgeyBzdHVkZW50SWQ6IHN0cmluZyB9KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3BheW1lbnRzJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHBheW1lbnREYXRhKSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjcmVhdGUgcGF5bWVudCcpO1xyXG4gICAgICBjb25zdCBuZXdQYXltZW50ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgICAgc2V0UGF5bWVudHMocHJldlBheW1lbnRzID0+IFtuZXdQYXltZW50LCAuLi5wcmV2UGF5bWVudHNdKTtcclxuXHJcbiAgICAgIC8vIE5vdGlmeSBhbGwgc3Vic2NyaWJlcnMgYWJvdXQgdGhlIHVwZGF0ZVxyXG4gICAgICBwYXltZW50RXZlbnRCdXMucHVibGlzaCgpO1xyXG4gICAgICByZXR1cm4gbmV3UGF5bWVudDtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgZGVsZXRlUGF5bWVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXltZW50SWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9wYXltZW50cz9pZD0ke3BheW1lbnRJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgcGF5bWVudCcpO1xyXG5cclxuICAgICAgc2V0UGF5bWVudHMocHJldlBheW1lbnRzID0+XHJcbiAgICAgICAgcHJldlBheW1lbnRzLmZpbHRlcihwYXltZW50ID0+IHBheW1lbnQuaWQgIT09IHBheW1lbnRJZClcclxuICAgICAgKTtcclxuXHJcbiAgICAgIC8vIE5vdGlmeSBhbGwgc3Vic2NyaWJlcnMgYWJvdXQgdGhlIHVwZGF0ZVxyXG4gICAgICBwYXltZW50RXZlbnRCdXMucHVibGlzaCgpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBnZXRTdHVkZW50UGF5bWVudFN0YXR1cyA9IHVzZUNhbGxiYWNrKChzdHVkZW50SWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3Qgc3R1ZGVudFBheW1lbnRzID0gcGF5bWVudHMuZmlsdGVyKHAgPT4gcC5zdHVkZW50SWQgPT09IHN0dWRlbnRJZCk7XHJcbiAgICBpZiAoc3R1ZGVudFBheW1lbnRzLmxlbmd0aCA9PT0gMCkgcmV0dXJuICd1bnBhaWQnO1xyXG5cclxuICAgIGNvbnN0IGhhc1VucGFpZFBheW1lbnRzID0gc3R1ZGVudFBheW1lbnRzLnNvbWUocCA9PiBwLnN0YXR1cyA9PT0gJ3VucGFpZCcpO1xyXG4gICAgcmV0dXJuIGhhc1VucGFpZFBheW1lbnRzID8gJ3VucGFpZCcgOiAncGFpZCc7XHJcbiAgfSwgW3BheW1lbnRzXSk7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBwYXltZW50cyxcclxuICAgIGxvYWRpbmcsXHJcbiAgICBlcnJvcixcclxuICAgIHVwZGF0ZVBheW1lbnQsXHJcbiAgICBjcmVhdGVQYXltZW50LFxyXG4gICAgZGVsZXRlUGF5bWVudCxcclxuICAgIGdldFN0dWRlbnRQYXltZW50U3RhdHVzLFxyXG4gICAgcmVmcmVzaFBheW1lbnRzOiBmZXRjaFBheW1lbnRzLFxyXG4gIH07XHJcbn07Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJwYXltZW50RXZlbnRCdXMiLCJsaXN0ZW5lcnMiLCJTZXQiLCJzdWJzY3JpYmUiLCJsaXN0ZW5lciIsImFkZCIsImRlbGV0ZSIsInB1Ymxpc2giLCJmb3JFYWNoIiwidXNlUGF5bWVudHMiLCJwYXltZW50cyIsInNldFBheW1lbnRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiZmV0Y2hQYXltZW50cyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJlcnIiLCJtZXNzYWdlIiwidW5zdWJzY3JpYmUiLCJ1cGRhdGVQYXltZW50IiwicGF5bWVudERhdGEiLCJpZCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInVwZGF0ZWRQYXltZW50IiwicHJldlBheW1lbnRzIiwibWFwIiwicGF5bWVudCIsImNyZWF0ZVBheW1lbnQiLCJuZXdQYXltZW50IiwiZGVsZXRlUGF5bWVudCIsInBheW1lbnRJZCIsImZpbHRlciIsImdldFN0dWRlbnRQYXltZW50U3RhdHVzIiwic3R1ZGVudElkIiwic3R1ZGVudFBheW1lbnRzIiwicCIsImxlbmd0aCIsImhhc1VucGFpZFBheW1lbnRzIiwic29tZSIsInN0YXR1cyIsInJlZnJlc2hQYXltZW50cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/usePayments.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticate: () => (/* binding */ authenticate),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isDashboardUser: () => (/* binding */ isDashboardUser),\n/* harmony export */   isReceptionUser: () => (/* binding */ isReceptionUser),\n/* harmony export */   isRestrictedPage: () => (/* binding */ isRestrictedPage),\n/* harmony export */   logout: () => (/* binding */ logout)\n/* harmony export */ });\nconst ADMINS = [\n    {\n        email: \"<EMAIL>\",\n        password: \"Parviz0106$\"\n    },\n    {\n        email: \"<EMAIL>\",\n        password: \"Reception123-\"\n    },\n    {\n        email: \"<EMAIL>\",\n        password: \"Mukhammadkhon0106$\"\n    }\n];\nconst authenticate = (email, password)=>{\n    const admin = ADMINS.find((a)=>a.email === email && a.password === password);\n    if (admin) {\n        localStorage.setItem(\"isAuthenticated\", \"true\");\n        localStorage.setItem(\"userEmail\", admin.email);\n        return true;\n    }\n    return false;\n};\nconst isAuthenticated = ()=>{\n    if (true) return false;\n    return localStorage.getItem(\"isAuthenticated\") === \"true\";\n};\nconst logout = ()=>{\n    localStorage.removeItem(\"isAuthenticated\");\n    localStorage.removeItem(\"userEmail\");\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"userEmail\");\n};\nconst isReceptionUser = ()=>{\n    const userEmail = getCurrentUser();\n    return userEmail === \"<EMAIL>\";\n};\nconst isDashboardUser = ()=>{\n    const userEmail = getCurrentUser();\n    return userEmail === \"<EMAIL>\" || userEmail === \"<EMAIL>\";\n};\nconst isRestrictedPage = (pathname)=>{\n    const restrictedPages = [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\"\n    ];\n    // Only apply restrictions for reception user\n    return isReceptionUser() && restrictedPages.includes(pathname);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/theme/DefaultColors.tsx":
/*!*******************************************!*\
  !*** ./src/utils/theme/DefaultColors.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baselightTheme: () => (/* binding */ baselightTheme),\n/* harmony export */   plus: () => (/* reexport default from dynamic */ next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\utils\\\\theme\\\\DefaultColors.tsx\",\"import\":\"Plus_Jakarta_Sans\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"display\":\"swap\",\"fallback\":[\"Helvetica\",\"Arial\",\"sans-serif\"]}],\"variableName\":\"plus\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\utils\\\\\\\\theme\\\\\\\\DefaultColors.tsx\\\",\\\"import\\\":\\\"Plus_Jakarta_Sans\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"fallback\\\":[\\\"Helvetica\\\",\\\"Arial\\\",\\\"sans-serif\\\"]}],\\\"variableName\\\":\\\"plus\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n\n\nconst baselightTheme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    direction: \"ltr\",\n    palette: {\n        primary: {\n            main: \"#5D87FF\",\n            light: \"#ECF2FF\",\n            dark: \"#4570EA\"\n        },\n        secondary: {\n            main: \"#49BEFF\",\n            light: \"#E8F7FF\",\n            dark: \"#23afdb\"\n        },\n        success: {\n            main: \"#13DEB9\",\n            light: \"#E6FFFA\",\n            dark: \"#02b3a9\",\n            contrastText: \"#ffffff\"\n        },\n        info: {\n            main: \"#539BFF\",\n            light: \"#EBF3FE\",\n            dark: \"#1682d4\",\n            contrastText: \"#ffffff\"\n        },\n        error: {\n            main: \"#FA896B\",\n            light: \"#FDEDE8\",\n            dark: \"#f3704d\",\n            contrastText: \"#ffffff\"\n        },\n        warning: {\n            main: \"#FFAE1F\",\n            light: \"#FEF5E5\",\n            dark: \"#ae8e59\",\n            contrastText: \"#ffffff\"\n        },\n        grey: {\n            100: \"#F2F6FA\",\n            200: \"#EAEFF4\",\n            300: \"#DFE5EF\",\n            400: \"#7C8FAC\",\n            500: \"#5A6A85\",\n            600: \"#2A3547\"\n        },\n        text: {\n            primary: \"#2A3547\",\n            secondary: \"#5A6A85\"\n        },\n        action: {\n            disabledBackground: \"rgba(73,82,88,0.12)\",\n            hoverOpacity: 0.02,\n            hover: \"#f6f9fc\"\n        },\n        divider: \"#e5eaef\"\n    },\n    typography: {\n        fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily,\n        h1: {\n            fontWeight: 600,\n            fontSize: \"2.25rem\",\n            lineHeight: \"2.75rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h2: {\n            fontWeight: 600,\n            fontSize: \"1.875rem\",\n            lineHeight: \"2.25rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h3: {\n            fontWeight: 600,\n            fontSize: \"1.5rem\",\n            lineHeight: \"1.75rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h4: {\n            fontWeight: 600,\n            fontSize: \"1.3125rem\",\n            lineHeight: \"1.6rem\"\n        },\n        h5: {\n            fontWeight: 600,\n            fontSize: \"1.125rem\",\n            lineHeight: \"1.6rem\"\n        },\n        h6: {\n            fontWeight: 600,\n            fontSize: \"1rem\",\n            lineHeight: \"1.2rem\"\n        },\n        button: {\n            textTransform: \"capitalize\",\n            fontWeight: 400\n        },\n        body1: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400,\n            lineHeight: \"1.334rem\"\n        },\n        body2: {\n            fontSize: \"0.75rem\",\n            letterSpacing: \"0rem\",\n            fontWeight: 400,\n            lineHeight: \"1rem\"\n        },\n        subtitle1: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400\n        },\n        subtitle2: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400\n        }\n    },\n    components: {\n        MuiCssBaseline: {\n            styleOverrides: {\n                \".MuiPaper-elevation9, .MuiPopover-root .MuiPaper-elevation\": {\n                    boxShadow: \"rgb(145 158 171 / 30%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 12px 24px -4px !important\"\n                }\n            }\n        },\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                    borderRadius: \"7px\"\n                }\n            }\n        }\n    }\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvdGhlbWUvRGVmYXVsdENvbG9ycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFHYUE7QUFIc0M7QUFVbkQsTUFBTUUsaUJBQWlCRCxnRUFBV0EsQ0FBQztJQUNqQ0UsV0FBVztJQUNYQyxTQUFTO1FBQ1BDLFNBQVM7WUFDUEMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxXQUFXO1lBQ1RILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUUsU0FBUztZQUNQSixNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNORyxjQUFjO1FBQ2hCO1FBQ0FDLE1BQU07WUFDSk4sTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkcsY0FBYztRQUNoQjtRQUNBRSxPQUFPO1lBQ0xQLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05HLGNBQWM7UUFDaEI7UUFDQUcsU0FBUztZQUNQUixNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNORyxjQUFjO1FBQ2hCO1FBQ0FJLE1BQU07WUFDSixLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7UUFDUDtRQUNBQyxNQUFNO1lBQ0pYLFNBQVM7WUFDVEksV0FBVztRQUNiO1FBQ0FRLFFBQVE7WUFDTkMsb0JBQW9CO1lBQ3BCQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtRQUNBQyxTQUFTO0lBQ1g7SUFDQUMsWUFBWTtRQUNWQyxZQUFZdkIsaVFBQVUsQ0FBQ3VCLFVBQVU7UUFDakNFLElBQUk7WUFDRkMsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FNLElBQUk7WUFDRkgsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FPLElBQUk7WUFDRkosWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FRLElBQUk7WUFDRkwsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7UUFDZDtRQUNBSSxJQUFJO1lBQ0ZOLFlBQVk7WUFDWkMsVUFBVTtZQUNWQyxZQUFZO1FBQ2Q7UUFDQUssSUFBSTtZQUNGUCxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsWUFBWTtRQUNkO1FBQ0FNLFFBQVE7WUFDTkMsZUFBZTtZQUNmVCxZQUFZO1FBQ2Q7UUFDQVUsT0FBTztZQUNMVCxVQUFVO1lBQ1ZELFlBQVk7WUFDWkUsWUFBWTtRQUNkO1FBQ0FTLE9BQU87WUFDTFYsVUFBVTtZQUNWVyxlQUFlO1lBQ2ZaLFlBQVk7WUFDWkUsWUFBWTtRQUNkO1FBQ0FXLFdBQVc7WUFDVFosVUFBVTtZQUNWRCxZQUFZO1FBQ2Q7UUFDQWMsV0FBVztZQUNUYixVQUFVO1lBQ1ZELFlBQVk7UUFDZDtJQUNGO0lBQ0FlLFlBQVk7UUFDVkMsZ0JBQWdCO1lBQ2RDLGdCQUFnQjtnQkFDZCw4REFBOEQ7b0JBQzVEQyxXQUNFO2dCQUNKO1lBQ0Y7UUFDRjtRQUNBQyxTQUFTO1lBQ1BGLGdCQUFnQjtnQkFDZEcsTUFBTTtvQkFDSkMsY0FBYztnQkFDaEI7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUUwQjtBQTdJYi9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy91dGlscy90aGVtZS9EZWZhdWx0Q29sb3JzLnRzeD9lMjM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVRoZW1lIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvc3R5bGVzXCI7XHJcbmltcG9ydCB7IFBsdXNfSmFrYXJ0YV9TYW5zIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBwbHVzID0gUGx1c19KYWthcnRhX1NhbnMoe1xyXG4gIHdlaWdodDogW1wiMzAwXCIsIFwiNDAwXCIsIFwiNTAwXCIsIFwiNjAwXCIsIFwiNzAwXCJdLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG4gIGRpc3BsYXk6IFwic3dhcFwiLFxyXG4gIGZhbGxiYWNrOiBbXCJIZWx2ZXRpY2FcIiwgXCJBcmlhbFwiLCBcInNhbnMtc2VyaWZcIl0sXHJcbn0pO1xyXG5cclxuY29uc3QgYmFzZWxpZ2h0VGhlbWUgPSBjcmVhdGVUaGVtZSh7XHJcbiAgZGlyZWN0aW9uOiBcImx0clwiLFxyXG4gIHBhbGV0dGU6IHtcclxuICAgIHByaW1hcnk6IHtcclxuICAgICAgbWFpbjogXCIjNUQ4N0ZGXCIsXHJcbiAgICAgIGxpZ2h0OiBcIiNFQ0YyRkZcIixcclxuICAgICAgZGFyazogXCIjNDU3MEVBXCIsXHJcbiAgICB9LFxyXG4gICAgc2Vjb25kYXJ5OiB7XHJcbiAgICAgIG1haW46IFwiIzQ5QkVGRlwiLFxyXG4gICAgICBsaWdodDogXCIjRThGN0ZGXCIsXHJcbiAgICAgIGRhcms6IFwiIzIzYWZkYlwiLFxyXG4gICAgfSxcclxuICAgIHN1Y2Nlc3M6IHtcclxuICAgICAgbWFpbjogXCIjMTNERUI5XCIsXHJcbiAgICAgIGxpZ2h0OiBcIiNFNkZGRkFcIixcclxuICAgICAgZGFyazogXCIjMDJiM2E5XCIsXHJcbiAgICAgIGNvbnRyYXN0VGV4dDogXCIjZmZmZmZmXCIsXHJcbiAgICB9LFxyXG4gICAgaW5mbzoge1xyXG4gICAgICBtYWluOiBcIiM1MzlCRkZcIixcclxuICAgICAgbGlnaHQ6IFwiI0VCRjNGRVwiLFxyXG4gICAgICBkYXJrOiBcIiMxNjgyZDRcIixcclxuICAgICAgY29udHJhc3RUZXh0OiBcIiNmZmZmZmZcIixcclxuICAgIH0sXHJcbiAgICBlcnJvcjoge1xyXG4gICAgICBtYWluOiBcIiNGQTg5NkJcIixcclxuICAgICAgbGlnaHQ6IFwiI0ZERURFOFwiLFxyXG4gICAgICBkYXJrOiBcIiNmMzcwNGRcIixcclxuICAgICAgY29udHJhc3RUZXh0OiBcIiNmZmZmZmZcIixcclxuICAgIH0sXHJcbiAgICB3YXJuaW5nOiB7XHJcbiAgICAgIG1haW46IFwiI0ZGQUUxRlwiLFxyXG4gICAgICBsaWdodDogXCIjRkVGNUU1XCIsXHJcbiAgICAgIGRhcms6IFwiI2FlOGU1OVwiLFxyXG4gICAgICBjb250cmFzdFRleHQ6IFwiI2ZmZmZmZlwiLFxyXG4gICAgfSxcclxuICAgIGdyZXk6IHtcclxuICAgICAgMTAwOiBcIiNGMkY2RkFcIixcclxuICAgICAgMjAwOiBcIiNFQUVGRjRcIixcclxuICAgICAgMzAwOiBcIiNERkU1RUZcIixcclxuICAgICAgNDAwOiBcIiM3QzhGQUNcIixcclxuICAgICAgNTAwOiBcIiM1QTZBODVcIixcclxuICAgICAgNjAwOiBcIiMyQTM1NDdcIixcclxuICAgIH0sXHJcbiAgICB0ZXh0OiB7XHJcbiAgICAgIHByaW1hcnk6IFwiIzJBMzU0N1wiLFxyXG4gICAgICBzZWNvbmRhcnk6IFwiIzVBNkE4NVwiLFxyXG4gICAgfSxcclxuICAgIGFjdGlvbjoge1xyXG4gICAgICBkaXNhYmxlZEJhY2tncm91bmQ6IFwicmdiYSg3Myw4Miw4OCwwLjEyKVwiLFxyXG4gICAgICBob3Zlck9wYWNpdHk6IDAuMDIsXHJcbiAgICAgIGhvdmVyOiBcIiNmNmY5ZmNcIixcclxuICAgIH0sXHJcbiAgICBkaXZpZGVyOiBcIiNlNWVhZWZcIixcclxuICB9LFxyXG4gIHR5cG9ncmFwaHk6IHtcclxuICAgIGZvbnRGYW1pbHk6IHBsdXMuc3R5bGUuZm9udEZhbWlseSxcclxuICAgIGgxOiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMi4yNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjIuNzVyZW1cIixcclxuICAgICAgZm9udEZhbWlseTogcGx1cy5zdHlsZS5mb250RmFtaWx5LFxyXG4gICAgfSxcclxuICAgIGgyOiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMS44NzVyZW1cIixcclxuICAgICAgbGluZUhlaWdodDogXCIyLjI1cmVtXCIsXHJcbiAgICAgIGZvbnRGYW1pbHk6IHBsdXMuc3R5bGUuZm9udEZhbWlseSxcclxuICAgIH0sXHJcbiAgICBoMzoge1xyXG4gICAgICBmb250V2VpZ2h0OiA2MDAsXHJcbiAgICAgIGZvbnRTaXplOiBcIjEuNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuNzVyZW1cIixcclxuICAgICAgZm9udEZhbWlseTogcGx1cy5zdHlsZS5mb250RmFtaWx5LFxyXG4gICAgfSxcclxuICAgIGg0OiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMS4zMTI1cmVtXCIsXHJcbiAgICAgIGxpbmVIZWlnaHQ6IFwiMS42cmVtXCIsXHJcbiAgICB9LFxyXG4gICAgaDU6IHtcclxuICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICBmb250U2l6ZTogXCIxLjEyNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuNnJlbVwiLFxyXG4gICAgfSxcclxuICAgIGg2OiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuMnJlbVwiLFxyXG4gICAgfSxcclxuICAgIGJ1dHRvbjoge1xyXG4gICAgICB0ZXh0VHJhbnNmb3JtOiBcImNhcGl0YWxpemVcIixcclxuICAgICAgZm9udFdlaWdodDogNDAwLFxyXG4gICAgfSxcclxuICAgIGJvZHkxOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgICAgbGluZUhlaWdodDogXCIxLjMzNHJlbVwiLFxyXG4gICAgfSxcclxuICAgIGJvZHkyOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuNzVyZW1cIixcclxuICAgICAgbGV0dGVyU3BhY2luZzogXCIwcmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgICAgbGluZUhlaWdodDogXCIxcmVtXCIsXHJcbiAgICB9LFxyXG4gICAgc3VidGl0bGUxOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgIH0sXHJcbiAgICBzdWJ0aXRsZTI6IHtcclxuICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgZm9udFdlaWdodDogNDAwLFxyXG4gICAgfSxcclxuICB9LFxyXG4gIGNvbXBvbmVudHM6IHtcclxuICAgIE11aUNzc0Jhc2VsaW5lOiB7XHJcbiAgICAgIHN0eWxlT3ZlcnJpZGVzOiB7XHJcbiAgICAgICAgXCIuTXVpUGFwZXItZWxldmF0aW9uOSwgLk11aVBvcG92ZXItcm9vdCAuTXVpUGFwZXItZWxldmF0aW9uXCI6IHtcclxuICAgICAgICAgIGJveFNoYWRvdzpcclxuICAgICAgICAgICAgXCJyZ2IoMTQ1IDE1OCAxNzEgLyAzMCUpIDBweCAwcHggMnB4IDBweCwgcmdiKDE0NSAxNTggMTcxIC8gMTIlKSAwcHggMTJweCAyNHB4IC00cHggIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgTXVpQ2FyZDoge1xyXG4gICAgICBzdHlsZU92ZXJyaWRlczoge1xyXG4gICAgICAgIHJvb3Q6IHtcclxuICAgICAgICAgIGJvcmRlclJhZGl1czogXCI3cHhcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICB9LFxyXG59KTtcclxuXHJcbmV4cG9ydCB7IGJhc2VsaWdodFRoZW1lIH07XHJcbiJdLCJuYW1lcyI6WyJwbHVzIiwiY3JlYXRlVGhlbWUiLCJiYXNlbGlnaHRUaGVtZSIsImRpcmVjdGlvbiIsInBhbGV0dGUiLCJwcmltYXJ5IiwibWFpbiIsImxpZ2h0IiwiZGFyayIsInNlY29uZGFyeSIsInN1Y2Nlc3MiLCJjb250cmFzdFRleHQiLCJpbmZvIiwiZXJyb3IiLCJ3YXJuaW5nIiwiZ3JleSIsInRleHQiLCJhY3Rpb24iLCJkaXNhYmxlZEJhY2tncm91bmQiLCJob3Zlck9wYWNpdHkiLCJob3ZlciIsImRpdmlkZXIiLCJ0eXBvZ3JhcGh5IiwiZm9udEZhbWlseSIsInN0eWxlIiwiaDEiLCJmb250V2VpZ2h0IiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwiaDIiLCJoMyIsImg0IiwiaDUiLCJoNiIsImJ1dHRvbiIsInRleHRUcmFuc2Zvcm0iLCJib2R5MSIsImJvZHkyIiwibGV0dGVyU3BhY2luZyIsInN1YnRpdGxlMSIsInN1YnRpdGxlMiIsImNvbXBvbmVudHMiLCJNdWlDc3NCYXNlbGluZSIsInN0eWxlT3ZlcnJpZGVzIiwiYm94U2hhZG93IiwiTXVpQ2FyZCIsInJvb3QiLCJib3JkZXJSYWRpdXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/theme/DefaultColors.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(DashboardLayout)/layout.tsx":
/*!**********************************************!*\
  !*** ./src/app/(DashboardLayout)/layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\(DashboardLayout)\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\(DashboardLayout)\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/(DashboardLayout)/loading.tsx":
/*!***********************************************!*\
  !*** ./src/app/(DashboardLayout)/loading.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Loading = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Dash Loading\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loading);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhEYXNoYm9hcmRMYXlvdXQpL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxNQUFNQSxVQUFVO0lBQ1oscUJBQ0ksOERBQUNDO2tCQUFJOzs7Ozs7QUFFYjtBQUVBLGlFQUFlRCxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9hcHAvKERhc2hib2FyZExheW91dCkvbG9hZGluZy50c3g/YTdhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBMb2FkaW5nID0gKCkgPT57XHJcbiAgICByZXR1cm4oXHJcbiAgICAgICAgPGRpdj5EYXNoIExvYWRpbmc8L2Rpdj5cclxuICAgIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgTG9hZGluZzsiXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(DashboardLayout)/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(DashboardLayout)/page.tsx":
/*!********************************************!*\
  !*** ./src/app/(DashboardLayout)/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\(DashboardLayout)\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\(DashboardLayout)\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Loading = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Loading\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loading);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxNQUFNQSxVQUFVO0lBQ1oscUJBQ0ksOERBQUNDO2tCQUFJOzs7Ozs7QUFFYjtBQUVBLGlFQUFlRCxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBMb2FkaW5nID0gKCkgPT57XHJcbiAgICByZXR1cm4oXHJcbiAgICAgICAgPGRpdj5Mb2FkaW5nPC9kaXY+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IExvYWRpbmc7Il0sIm5hbWVzIjpbIkxvYWRpbmciLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLy4vc3JjL2FwcC9mYXZpY29uLmljbz85YzhiIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/react-transition-group","vendor-chunks/stylis","vendor-chunks/react-helmet-async","vendor-chunks/hoist-non-react-statics","vendor-chunks/react-is","vendor-chunks/react-fast-compare","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/invariant","vendor-chunks/shallowequal","vendor-chunks/clsx","vendor-chunks/@tabler","vendor-chunks/lodash"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(DashboardLayout)%2Fpage&page=%2F(DashboardLayout)%2Fpage&appPaths=%2F(DashboardLayout)%2Fpage&pagePath=private-next-app-dir%2F(DashboardLayout)%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();