import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Only admins can view automation logs
    if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' }, 
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const ruleId = searchParams.get('ruleId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (ruleId) {
      where.ruleId = ruleId;
    }
    
    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.executedAt = {};
      if (startDate) {
        where.executedAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.executedAt.lte = new Date(endDate);
      }
    }

    const [logs, total] = await Promise.all([
      prisma.automationLog.findMany({
        where,
        include: {
          rule: {
            select: {
              id: true,
              name: true,
              trigger: true,
              isActive: true
            }
          }
        },
        orderBy: {
          executedAt: 'desc'
        },
        skip,
        take: limit,
      }),
      prisma.automationLog.count({ where })
    ]);

    // Get summary statistics
    const stats = await prisma.automationLog.groupBy({
      by: ['status'],
      where: {
        executedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      _count: {
        _all: true
      }
    });

    const summary = stats.reduce((acc, stat) => {
      acc[stat.status] = stat._count._all;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        last24Hours: summary,
        total: {
          success: summary.SUCCESS || 0,
          failed: summary.FAILED || 0,
          skipped: summary.SKIPPED || 0
        }
      }
    });
  } catch (error) {
    console.error('Failed to fetch automation logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch automation logs' }, 
      { status: 500 }
    );
  }
}
