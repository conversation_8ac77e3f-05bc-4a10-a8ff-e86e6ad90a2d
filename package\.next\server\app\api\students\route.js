"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/students/route";
exports.ids = ["app/api/students/route"];
exports.modules = {

/***/ "@prisma/client?9bbd":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_Improved_CRM_package_src_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/students/route.ts */ \"(rsc)/./src/app/api/students/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/students/route\",\n        pathname: \"/api/students\",\n        filename: \"route\",\n        bundlePath: \"app/api/students/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\api\\\\students\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_Improved_CRM_package_src_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/students/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/students/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/students/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?9bbd\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request) {\n    try {\n        // Get user info from headers (set by middleware)\n        const userRole = request.headers.get(\"x-user-role\");\n        const userId = request.headers.get(\"x-user-id\");\n        if (!userId || !userRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check permissions - only Admin and Reception can view all students\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.hasPermission(userRole, _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        const students = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        isActive: true\n                    }\n                },\n                classes: {\n                    select: {\n                        id: true,\n                        name: true,\n                        subject: true,\n                        level: true\n                    }\n                },\n                payments: {\n                    select: {\n                        id: true,\n                        amount: true,\n                        date: true,\n                        status: true\n                    },\n                    orderBy: {\n                        date: \"desc\"\n                    },\n                    take: 5\n                },\n                _count: {\n                    select: {\n                        attendances: true,\n                        grades: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Log the action\n        await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.logAuditEvent(userId, \"READ\", \"Student\", \"all\", null, null, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(students);\n    } catch (error) {\n        console.error(\"Get students error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to fetch students\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Get user info from headers (set by middleware)\n        const userRole = request.headers.get(\"x-user-role\");\n        const userId = request.headers.get(\"x-user-id\");\n        if (!userId || !userRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check permissions - only Admin and Reception can create students\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.hasPermission(userRole, _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        // Validate input\n        const validationResult = _lib_validation__WEBPACK_IMPORTED_MODULE_2__.studentSchema.safeParse(body);\n        if (!validationResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Validation failed\",\n                details: validationResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const data = validationResult.data;\n        // Create user account if email is provided\n        let userAccount = null;\n        if (data.email) {\n            // Generate a temporary password\n            const tempPassword = Math.random().toString(36).slice(-8) + \"A1!\";\n            const hashedPassword = await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.hashPassword(tempPassword);\n            userAccount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n                data: {\n                    email: data.email,\n                    password: hashedPassword,\n                    name: data.name,\n                    role: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.STUDENT,\n                    isActive: true\n                }\n            });\n        }\n        const student = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.create({\n            data: {\n                userId: userAccount?.id,\n                name: data.name,\n                phone: data.phone,\n                joinDate: new Date(data.joinDate),\n                paymentStatus: data.paymentStatus || \"UNPAID\",\n                status: data.status || \"INQUIRY\",\n                parentName: data.parentName,\n                parentPhone: data.parentPhone,\n                emergencyContact: data.emergencyContact,\n                address: data.address,\n                dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,\n                ...data.classIds && {\n                    classes: {\n                        connect: data.classIds.map((id)=>({\n                                id\n                            }))\n                    }\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        isActive: true\n                    }\n                },\n                classes: {\n                    select: {\n                        id: true,\n                        name: true,\n                        subject: true,\n                        level: true\n                    }\n                }\n            }\n        });\n        // Log the action\n        await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.logAuditEvent(userId, \"CREATE\", \"Student\", student.id, null, student, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(student);\n    } catch (error) {\n        console.error(\"Create student error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to create student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        // Get user info from headers (set by middleware)\n        const userRole = request.headers.get(\"x-user-role\");\n        const userId = request.headers.get(\"x-user-id\");\n        if (!userId || !userRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check permissions - only Admin and Reception can update students\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.hasPermission(userRole, _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { id, ...updateData } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Student ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate input\n        const validationResult = _lib_validation__WEBPACK_IMPORTED_MODULE_2__.studentSchema.partial().safeParse(updateData);\n        if (!validationResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Validation failed\",\n                details: validationResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const data = validationResult.data;\n        // Get current student data for audit log\n        const currentStudent = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: true,\n                classes: true\n            }\n        });\n        if (!currentStudent) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        const student = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.update({\n            where: {\n                id\n            },\n            data: {\n                name: data.name,\n                phone: data.phone,\n                joinDate: data.joinDate ? new Date(data.joinDate) : undefined,\n                paymentStatus: data.paymentStatus,\n                status: data.status,\n                parentName: data.parentName,\n                parentPhone: data.parentPhone,\n                emergencyContact: data.emergencyContact,\n                address: data.address,\n                dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : undefined,\n                ...data.classIds && {\n                    classes: {\n                        set: data.classIds.map((id)=>({\n                                id\n                            }))\n                    }\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        isActive: true\n                    }\n                },\n                classes: {\n                    select: {\n                        id: true,\n                        name: true,\n                        subject: true,\n                        level: true\n                    }\n                }\n            }\n        });\n        // Log the action\n        await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.logAuditEvent(userId, \"UPDATE\", \"Student\", student.id, currentStudent, student, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(student);\n    } catch (error) {\n        console.error(\"Update student error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to update student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Get user info from headers (set by middleware)\n        const userRole = request.headers.get(\"x-user-role\");\n        const userId = request.headers.get(\"x-user-id\");\n        if (!userId || !userRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check permissions - only Admin can delete students\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.hasPermission(userRole, _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.ADMIN)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Student ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get current student data for audit log\n        const currentStudent = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: true,\n                classes: true,\n                payments: true\n            }\n        });\n        if (!currentStudent) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Delete related user account if exists\n        if (currentStudent.userId) {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.delete({\n                where: {\n                    id: currentStudent.userId\n                }\n            });\n        }\n        // Delete student (cascade will handle related records)\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.student.delete({\n            where: {\n                id\n            }\n        });\n        // Log the action\n        await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.AuthService.logAuditEvent(userId, \"DELETE\", \"Student\", id, currentStudent, null, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            message: \"Student deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Delete student error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to delete student\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/students/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?9bbd\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-in-production\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key-change-in-production\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"15m\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nclass AuthService {\n    // Hash password\n    static async hashPassword(password) {\n        const saltRounds = 12;\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n    }\n    // Verify password\n    static async verifyPassword(password, hashedPassword) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n    }\n    // Generate JWT tokens\n    static generateTokens(payload) {\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Login user\n    static async login(email, password, ipAddress, userAgent) {\n        try {\n            // Find user by email\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                where: {\n                    email: email.toLowerCase()\n                }\n            });\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Check if user is active\n            if (!user.isActive) {\n                return {\n                    success: false,\n                    error: \"Account is deactivated\"\n                };\n            }\n            // Check if user is locked\n            if (user.isLocked) {\n                return {\n                    success: false,\n                    error: \"Account is locked due to too many failed attempts\"\n                };\n            }\n            // Verify password\n            const isPasswordValid = await this.verifyPassword(password, user.password);\n            if (!isPasswordValid) {\n                // Increment failed attempts\n                await this.handleFailedLogin(user.id);\n                return {\n                    success: false,\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Reset failed attempts on successful login\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    failedAttempts: 0,\n                    lastFailedAttempt: null,\n                    lastLoginAt: new Date()\n                }\n            });\n            // Generate tokens\n            const payload = {\n                userId: user.id,\n                email: user.email,\n                role: user.role,\n                name: user.name\n            };\n            const { accessToken, refreshToken } = this.generateTokens(payload);\n            // Store refresh token in database\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    refreshToken\n                }\n            });\n            // Log successful login\n            await this.logAuditEvent(user.id, \"LOGIN\", \"User\", user.id, null, null, ipAddress, userAgent);\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                },\n                accessToken,\n                refreshToken\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during login\"\n            };\n        }\n    }\n    // Handle failed login attempts\n    static async handleFailedLogin(userId) {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        if (!user) return;\n        const newFailedAttempts = user.failedAttempts + 1;\n        const shouldLock = newFailedAttempts >= 5;\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                failedAttempts: newFailedAttempts,\n                lastFailedAttempt: new Date(),\n                isLocked: shouldLock\n            }\n        });\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            const payload = this.verifyToken(refreshToken, true);\n            if (!payload) {\n                return {\n                    success: false,\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Verify refresh token exists in database\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                where: {\n                    id: payload.userId,\n                    refreshToken,\n                    isActive: true\n                }\n            });\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Generate new tokens\n            const newPayload = {\n                userId: user.id,\n                email: user.email,\n                role: user.role,\n                name: user.name\n            };\n            const tokens = this.generateTokens(newPayload);\n            // Update refresh token in database\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    refreshToken: tokens.refreshToken\n                }\n            });\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                },\n                accessToken: tokens.accessToken,\n                refreshToken: tokens.refreshToken\n            };\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during token refresh\"\n            };\n        }\n    }\n    // Logout user\n    static async logout(userId, ipAddress, userAgent) {\n        try {\n            // Clear refresh token\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: userId\n                },\n                data: {\n                    refreshToken: null\n                }\n            });\n            // Log logout\n            await this.logAuditEvent(userId, \"LOGOUT\", \"User\", userId, null, null, ipAddress, userAgent);\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    }\n    // Register new user\n    static async register(email, password, name, role = _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.STUDENT, ipAddress, userAgent) {\n        try {\n            // Check if user already exists\n            const existingUser = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                where: {\n                    email: email.toLowerCase()\n                }\n            });\n            if (existingUser) {\n                return {\n                    success: false,\n                    error: \"User with this email already exists\"\n                };\n            }\n            // Hash password\n            const hashedPassword = await this.hashPassword(password);\n            // Create user\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.create({\n                data: {\n                    email: email.toLowerCase(),\n                    password: hashedPassword,\n                    name,\n                    role\n                }\n            });\n            // Log user creation\n            await this.logAuditEvent(user.id, \"CREATE\", \"User\", user.id, null, {\n                email,\n                name,\n                role\n            }, ipAddress, userAgent);\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                }\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during registration\"\n            };\n        }\n    }\n    // Log audit events\n    static async logAuditEvent(userId, action, entity, entityId, oldData, newData, ipAddress, userAgent) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n                data: {\n                    userId,\n                    action,\n                    entity,\n                    entityId,\n                    oldData: oldData ? JSON.stringify(oldData) : null,\n                    newData: newData ? JSON.stringify(newData) : null,\n                    ipAddress,\n                    userAgent\n                }\n            });\n        } catch (error) {\n            console.error(\"Audit log error:\", error);\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        return _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                id: true,\n                email: true,\n                name: true,\n                role: true,\n                isActive: true,\n                lastLoginAt: true,\n                createdAt: true\n            }\n        });\n    }\n    // Check permissions\n    static hasPermission(userRole, requiredRole) {\n        const roleHierarchy = {\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.STUDENT]: 0,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.TEACHER]: 1,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.RECEPTION]: 2,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.ADMIN]: 3\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?9bbd\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUVqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfVxyXG5cclxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWwiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attendanceSchema: () => (/* binding */ attendanceSchema),\n/* harmony export */   cabinetSchema: () => (/* binding */ cabinetSchema),\n/* harmony export */   changePasswordSchema: () => (/* binding */ changePasswordSchema),\n/* harmony export */   classSchema: () => (/* binding */ classSchema),\n/* harmony export */   gradeSchema: () => (/* binding */ gradeSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   paymentSchema: () => (/* binding */ paymentSchema),\n/* harmony export */   registerSchema: () => (/* binding */ registerSchema),\n/* harmony export */   studentSchema: () => (/* binding */ studentSchema),\n/* harmony export */   teacherSchema: () => (/* binding */ teacherSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Authentication schemas\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, \"Password must be at least 6 characters\")\n});\nconst registerSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, \"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\"),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"ADMIN\",\n        \"RECEPTION\",\n        \"TEACHER\",\n        \"STUDENT\"\n    ]).default(\"STUDENT\")\n});\nconst changePasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    currentPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Current password is required\"),\n    newPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, \"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n}).refine((data)=>data.newPassword === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Student schemas\nconst studentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10, \"Phone number must be at least 10 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\").optional(),\n    joinDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    paymentStatus: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PAID\",\n        \"UNPAID\",\n        \"PARTIAL\"\n    ]).default(\"UNPAID\"),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"INQUIRY\",\n        \"LEAD\",\n        \"ENROLLED\",\n        \"GRADUATED\",\n        \"DROPPED\",\n        \"SUSPENDED\"\n    ]).default(\"INQUIRY\"),\n    parentName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    parentPhone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    emergencyContact: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    classIds: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional()\n});\n// Teacher schemas\nconst teacherSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10, \"Phone number must be at least 10 characters\"),\n    subjects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).min(1, \"At least one subject is required\"),\n    qualifications: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).min(1, \"At least one qualification is required\"),\n    joinDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"ACTIVE\",\n        \"INACTIVE\",\n        \"ON_LEAVE\"\n    ]).default(\"ACTIVE\"),\n    salary: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    emergencyContact: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Class schemas\nconst classSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Class name must be at least 2 characters\"),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Subject must be at least 2 characters\"),\n    level: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Level is required\"),\n    stage: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"EARLY\",\n        \"MIDDLE\",\n        \"LATE\"\n    ]).default(\"EARLY\"),\n    language: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"RUSSIAN\",\n        \"UZBEK\",\n        \"MIXED\"\n    ]).default(\"RUSSIAN\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    cabinetId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Cabinet is required\"),\n    courseAmount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Course amount must be positive\"),\n    maxStudents: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive().default(15),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    openingDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    schedule: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        day: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        startTime: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        endTime: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })),\n    studentIds: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional()\n});\n// Payment schemas\nconst paymentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Amount must be positive\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PENDING\",\n        \"COMPLETED\",\n        \"FAILED\",\n        \"REFUNDED\"\n    ]).default(\"PENDING\"),\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"CASH\",\n        \"CARD\",\n        \"BANK_TRANSFER\",\n        \"ONLINE\"\n    ]),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Description is required\"),\n    invoiceNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional()\n});\n// Cabinet schemas\nconst cabinetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Cabinet name is required\"),\n    capacity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(\"Capacity must be positive\"),\n    equipment: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).default([]),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"AVAILABLE\",\n        \"OCCUPIED\",\n        \"MAINTENANCE\"\n    ]).default(\"AVAILABLE\"),\n    location: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Location is required\")\n});\n// Attendance schemas\nconst attendanceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    classId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Class is required\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PRESENT\",\n        \"ABSENT\",\n        \"LATE\",\n        \"EXCUSED\"\n    ]),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Grade schemas\nconst gradeSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    classId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Class is required\"),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Subject is required\"),\n    gradeType: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"QUIZ\",\n        \"EXAM\",\n        \"HOMEWORK\",\n        \"PROJECT\"\n    ]),\n    score: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, \"Score cannot be negative\"),\n    maxScore: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Max score must be positive\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n}).refine((data)=>data.score <= data.maxScore, {\n    message: \"Score cannot be greater than max score\",\n    path: [\n        \"score\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();