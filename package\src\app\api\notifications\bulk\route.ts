import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuditService } from '@/lib/audit';

export async function PATCH(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, notificationIds } = body;

    if (!action || !Array.isArray(notificationIds)) {
      return NextResponse.json(
        { error: 'Invalid request body' }, 
        { status: 400 }
      );
    }

    let updateData: any = {};
    let actionDescription = '';

    switch (action) {
      case 'markAsRead':
        updateData = { isRead: true };
        actionDescription = 'Mark as read';
        break;
      case 'markAsUnread':
        updateData = { isRead: false };
        actionDescription = 'Mark as unread';
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' }, 
          { status: 400 }
        );
    }

    // Update notifications that belong to the current user
    const result = await prisma.notification.updateMany({
      where: {
        id: { in: notificationIds },
        userId: userId // Ensure user can only update their own notifications
      },
      data: updateData
    });

    // Log the bulk action
    await AuditService.log(
      userId,
      'BULK_UPDATE',
      'Notification',
      'bulk',
      null,
      { action: actionDescription, count: result.count, notificationIds },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({
      success: true,
      updatedCount: result.count,
      message: `${actionDescription} applied to ${result.count} notifications`
    });
  } catch (error) {
    console.error('Failed to perform bulk action on notifications:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { notificationIds } = body;

    if (!Array.isArray(notificationIds)) {
      return NextResponse.json(
        { error: 'Invalid request body' }, 
        { status: 400 }
      );
    }

    // Delete notifications that belong to the current user
    const result = await prisma.notification.deleteMany({
      where: {
        id: { in: notificationIds },
        userId: userId // Ensure user can only delete their own notifications
      }
    });

    // Log the bulk deletion
    await AuditService.log(
      userId,
      'BULK_DELETE',
      'Notification',
      'bulk',
      null,
      { action: 'Bulk delete', count: result.count, notificationIds },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({
      success: true,
      deletedCount: result.count,
      message: `${result.count} notifications deleted`
    });
  } catch (error) {
    console.error('Failed to delete notifications:', error);
    return NextResponse.json(
      { error: 'Failed to delete notifications' }, 
      { status: 500 }
    );
  }
}
