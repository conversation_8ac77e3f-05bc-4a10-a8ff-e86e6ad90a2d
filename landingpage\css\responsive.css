/*
   Innovative Centre - Responsive Stylesheet
   Author: Augment Agent
   Version: 1.0
*/

/* Large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }

    .hero-content h1 {
        font-size: 42px;
    }

    .features-grid,
    .courses-grid,
    .resources-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-nav {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }

    .header-wrapper {
        position: relative;
    }

    .main-nav {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #fff;
        box-shadow: var(--shadow);
        padding: 20px;
        border-radius: 0 0 10px 10px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: var(--transition);
        z-index: 100;
    }

    .main-nav.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .nav-list {
        flex-direction: column;
        gap: 15px;
    }

    .nav-link {
        color: var(--text-color);
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        padding-left: 20px;
        display: none;
    }

    .dropdown.active .dropdown-menu {
        display: block;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .hero-content p {
        font-size: 18px;
    }

    /* Improved hero section responsiveness */
    .hero-wrapper {
        flex-direction: column;
    }

    .hero-content {
        text-align: center;
        margin-bottom: 30px;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-form-container {
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }

    .exam-card,
    .cta-wrapper {
        flex-direction: column;
    }

    .exam-card.reverse {
        flex-direction: column;
    }

    .cta-content {
        padding: 30px;
    }

    .cta-content h2 {
        font-size: 26px;
    }

    .exam-image {
        width: 100%;
        height: 300px;
    }

    /* Improved video testimonials grid */
    .video-testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .video-grid-right {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonial-info {
        flex-direction: column;
        text-align: center;
    }

    .testimonial-image {
        margin: 0 auto;
    }

    .test-english-banner {
        flex-direction: column;
    }

    .banner-image {
        width: 100%;
    }

    /* Team Carousel Responsive */
    .team-categories {
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .category-btn {
        padding: 6px 15px;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .team-carousel .swiper-button-next,
    .team-carousel .swiper-button-prev {
        width: 35px;
        height: 35px;
    }

    /* Form improvements */
    .form-row {
        flex-wrap: wrap;
    }

    .form-group {
        flex: 0 0 100%;
    }

    /* Footer improvements */
    .footer-contact {
        flex-direction: column;
        gap: 15px;
    }
}

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }

    section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 30px;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .stats-wrapper {
        flex-direction: column;
        gap: 30px;
    }

    /* IELTS Results Responsive */
    .ielts-results-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .ielts-motivation-card {
        margin: 20px 0;
    }

    .mirror-effect {
        width: 150px;
        height: 180px;
    }

    .person-silhouette {
        width: 120px;
        height: 120px;
    }

    .features-grid,
    .courses-grid,
    .resources-grid {
        grid-template-columns: 1fr;
    }

    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Improved course cards */
    .course-card {
        padding: 25px 20px;
    }

    .course-features li {
        font-size: 14px;
    }

    /* Improved video grid */
    .video-grid-right {
        grid-template-columns: 1fr;
    }

    /* Improved FAQ section */
    .faq-item {
        padding: 15px;
    }

    .faq-question {
        font-size: 16px;
    }

    .faq-answer {
        font-size: 14px;
    }

    /* Improved footer */
    .footer-top {
        flex-direction: column;
        gap: 30px;
    }

    .footer-nav {
        grid-template-columns: 1fr;
    }

    .footer-contact-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .footer-contact-item {
        margin-bottom: 10px;
    }

    /* Improved form elements */
    input, select, textarea, button {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 20px;
    }

    .hero-content h1 {
        font-size: 28px;
    }

    .hero-content p {
        font-size: 16px;
    }

    .section-title {
        font-size: 26px;
    }

    .section-subtitle {
        font-size: 16px;
    }

    .exam-content {
        padding: 30px 20px;
    }

    .exam-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    /* Team Carousel Responsive - Extra Small */
    .team-categories {
        gap: 5px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .category-btn {
        padding: 5px 12px;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .team-carousel .swiper-button-next,
    .team-carousel .swiper-button-prev {
        display: none;
    }

    /* Improved header responsiveness */
    .header-wrapper {
        flex-wrap: wrap;
    }

    .header-right {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }

    .header-phone span {
        display: none;
    }

    /* Video testimonials grid improvements */
    .video-testimonials-grid {
        display: block;
    }

    .video-grid-right {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 15px;
    }

    /* IELTS Results Responsive - Extra Small */
    .result-image {
        height: 200px;
    }

    .carousel-result img {
        height: 120px;
    }

    .results-carousel-container {
        padding: 20px 15px;
    }

    .results-carousel .swiper-button-next,
    .results-carousel .swiper-button-prev {
        width: 30px;
        height: 30px;
    }

    .results-carousel .swiper-button-next:after,
    .results-carousel .swiper-button-prev:after {
        font-size: 14px;
    }

    .video-testimonial-item {
        margin-bottom: 15px;
    }

    /* Form improvements */
    .hero-form {
        padding: 20px 15px;
    }

    .checkbox-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .checkbox-item {
        margin-bottom: 8px;
    }

    /* Footer improvements */
    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Very small devices (small phones, less than 400px) */
@media (max-width: 399.98px) {
    .hero-content h1 {
        font-size: 24px;
    }

    /* Hide carousel navigation on very small screens */
    .results-carousel .swiper-button-next,
    .results-carousel .swiper-button-prev {
        display: none;
    }

    /* Smaller mirror effect for very small screens */
    .mirror-effect {
        width: 120px;
        height: 150px;
    }

    .person-silhouette {
        width: 100px;
        height: 100px;
    }

    .section-title {
        font-size: 22px;
    }

    .hero-form-container {
        padding: 15px 10px;
    }

    .hero-form h3 {
        font-size: 18px;
    }

    .form-subtitle {
        font-size: 14px;
    }

    .countdown-item {
        padding: 5px;
    }

    .countdown-number {
        font-size: 18px;
    }

    .countdown-label {
        font-size: 10px;
    }

    /* Improve touch targets for mobile */
    .btn {
        padding: 12px 15px;
        min-height: 44px;
    }

    input[type="checkbox"],
    input[type="radio"] {
        min-width: 20px;
        min-height: 20px;
    }

    .nav-item {
        margin-bottom: 10px;
    }
}
