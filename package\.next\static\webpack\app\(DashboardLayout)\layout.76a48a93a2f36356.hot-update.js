"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(DashboardLayout)/layout",{

/***/ "(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@mui/material/CircularProgress/CircularProgress.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _mui_utils_chainPropTypes__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/utils/chainPropTypes */ \"(app-pages-browser)/./node_modules/@mui/utils/chainPropTypes/chainPropTypes.js\");\n/* harmony import */ var _mui_utils_composeClasses__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/utils/composeClasses */ \"(app-pages-browser)/./node_modules/@mui/utils/composeClasses/composeClasses.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@emotion/react/dist/emotion-react.browser.esm.js\");\n/* harmony import */ var _utils_capitalize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/capitalize */ \"(app-pages-browser)/./node_modules/@mui/material/utils/capitalize.js\");\n/* harmony import */ var _styles_useThemeProps__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../styles/useThemeProps */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useThemeProps.js\");\n/* harmony import */ var _styles_styled__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../styles/styled */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _circularProgressClasses__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./circularProgressClasses */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/circularProgressClasses.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% {\\n    stroke-dasharray: 1px, 200px;\\n    stroke-dashoffset: 0;\\n  }\\n\\n  50% {\\n    stroke-dasharray: 100px, 200px;\\n    stroke-dashoffset: -15px;\\n  }\\n\\n  100% {\\n    stroke-dasharray: 100px, 200px;\\n    stroke-dashoffset: -125px;\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      animation: \",\n        \" 1.4s linear infinite;\\n    \"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      animation: \",\n        \" 1.4s ease-in-out infinite;\\n    \"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\n\n\nconst _excluded = [\n    \"className\",\n    \"color\",\n    \"disableShrink\",\n    \"size\",\n    \"style\",\n    \"thickness\",\n    \"value\",\n    \"variant\"\n];\nlet _ = (t)=>t, _t, _t2, _t3, _t4;\n\n\n\n\n\n\n\n\n\n\n\nconst SIZE = 44;\nconst circularRotateKeyframe = (0,_mui_system__WEBPACK_IMPORTED_MODULE_6__.keyframes)(_t || (_t = _(_templateObject())));\nconst circularDashKeyframe = (0,_mui_system__WEBPACK_IMPORTED_MODULE_6__.keyframes)(_t2 || (_t2 = _(_templateObject1())));\nconst useUtilityClasses = (ownerState)=>{\n    const { classes, variant, color, disableShrink } = ownerState;\n    const slots = {\n        root: [\n            \"root\",\n            variant,\n            \"color\".concat((0,_utils_capitalize__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(color))\n        ],\n        svg: [\n            \"svg\"\n        ],\n        circle: [\n            \"circle\",\n            \"circle\".concat((0,_utils_capitalize__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(variant)),\n            disableShrink && \"circleDisableShrink\"\n        ]\n    };\n    return (0,_mui_utils_composeClasses__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(slots, _circularProgressClasses__WEBPACK_IMPORTED_MODULE_9__.getCircularProgressUtilityClass, classes);\n};\nconst CircularProgressRoot = (0,_styles_styled__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(\"span\", {\n    name: \"MuiCircularProgress\",\n    slot: \"Root\",\n    overridesResolver: (props, styles)=>{\n        const { ownerState } = props;\n        return [\n            styles.root,\n            styles[ownerState.variant],\n            styles[\"color\".concat((0,_utils_capitalize__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(ownerState.color))]\n        ];\n    }\n})((param)=>{\n    let { ownerState, theme } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        display: \"inline-block\"\n    }, ownerState.variant === \"determinate\" && {\n        transition: theme.transitions.create(\"transform\")\n    }, ownerState.color !== \"inherit\" && {\n        color: (theme.vars || theme).palette[ownerState.color].main\n    });\n}, (param)=>{\n    let { ownerState } = param;\n    return ownerState.variant === \"indeterminate\" && (0,_mui_system__WEBPACK_IMPORTED_MODULE_6__.css)(_t3 || (_t3 = _(_templateObject2(), 0)), circularRotateKeyframe);\n});\nconst CircularProgressSVG = (0,_styles_styled__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(\"svg\", {\n    name: \"MuiCircularProgress\",\n    slot: \"Svg\",\n    overridesResolver: (props, styles)=>styles.svg\n})({\n    display: \"block\" // Keeps the progress centered\n});\nconst CircularProgressCircle = (0,_styles_styled__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(\"circle\", {\n    name: \"MuiCircularProgress\",\n    slot: \"Circle\",\n    overridesResolver: (props, styles)=>{\n        const { ownerState } = props;\n        return [\n            styles.circle,\n            styles[\"circle\".concat((0,_utils_capitalize__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(ownerState.variant))],\n            ownerState.disableShrink && styles.circleDisableShrink\n        ];\n    }\n})((param)=>{\n    let { ownerState, theme } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        stroke: \"currentColor\"\n    }, ownerState.variant === \"determinate\" && {\n        transition: theme.transitions.create(\"stroke-dashoffset\")\n    }, ownerState.variant === \"indeterminate\" && {\n        // Some default value that looks fine waiting for the animation to kicks in.\n        strokeDasharray: \"80px, 200px\",\n        strokeDashoffset: 0 // Add the unit to fix a Edge 16 and below bug.\n    });\n}, (param)=>{\n    let { ownerState } = param;\n    return ownerState.variant === \"indeterminate\" && !ownerState.disableShrink && (0,_mui_system__WEBPACK_IMPORTED_MODULE_6__.css)(_t4 || (_t4 = _(_templateObject3(), 0)), circularDashKeyframe);\n});\n/**\n * ## ARIA\n *\n * If the progress bar is describing the loading progress of a particular region of a page,\n * you should use `aria-describedby` to point to the progress bar, and set the `aria-busy`\n * attribute to `true` on that region until it has finished loading.\n */ const CircularProgress = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(_c = _s(function CircularProgress(inProps, ref) {\n    _s();\n    const props = (0,_styles_useThemeProps__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n        props: inProps,\n        name: \"MuiCircularProgress\"\n    });\n    const { className, color = \"primary\", disableShrink = false, size = 40, style, thickness = 3.6, value = 0, variant = \"indeterminate\" } = props, other = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n    const ownerState = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props, {\n        color,\n        disableShrink,\n        size,\n        thickness,\n        value,\n        variant\n    });\n    const classes = useUtilityClasses(ownerState);\n    const circleStyle = {};\n    const rootStyle = {};\n    const rootProps = {};\n    if (variant === \"determinate\") {\n        const circumference = 2 * Math.PI * ((SIZE - thickness) / 2);\n        circleStyle.strokeDasharray = circumference.toFixed(3);\n        rootProps[\"aria-valuenow\"] = Math.round(value);\n        circleStyle.strokeDashoffset = \"\".concat(((100 - value) / 100 * circumference).toFixed(3), \"px\");\n        rootStyle.transform = \"rotate(-90deg)\";\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(CircularProgressRoot, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(classes.root, className),\n        style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            width: size,\n            height: size\n        }, rootStyle, style),\n        ownerState: ownerState,\n        ref: ref,\n        role: \"progressbar\"\n    }, rootProps, other, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(CircularProgressSVG, {\n            className: classes.svg,\n            ownerState: ownerState,\n            viewBox: \"\".concat(SIZE / 2, \" \").concat(SIZE / 2, \" \").concat(SIZE, \" \").concat(SIZE),\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(CircularProgressCircle, {\n                className: classes.circle,\n                style: circleStyle,\n                ownerState: ownerState,\n                cx: SIZE,\n                cy: SIZE,\n                r: (SIZE - thickness) / 2,\n                fill: \"none\",\n                strokeWidth: thickness\n            })\n        })\n    }));\n}, \"526RDEpO1CGEADq5vnCvoNyCejQ=\", false, function() {\n    return [\n        _styles_useThemeProps__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        useUtilityClasses\n    ];\n})), \"526RDEpO1CGEADq5vnCvoNyCejQ=\", false, function() {\n    return [\n        _styles_useThemeProps__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        useUtilityClasses\n    ];\n});\n_c1 = CircularProgress;\n true ? CircularProgress.propTypes = {\n    // ┌────────────────────────────── Warning ──────────────────────────────┐\n    // │ These PropTypes are generated from the TypeScript type definitions. │\n    // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n    // └─────────────────────────────────────────────────────────────────────┘\n    /**\n   * Override or extend the styles applied to the component.\n   */ classes: (prop_types__WEBPACK_IMPORTED_MODULE_12___default().object),\n    /**\n   * @ignore\n   */ className: (prop_types__WEBPACK_IMPORTED_MODULE_12___default().string),\n    /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */ color: prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOfType([\n        prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOf([\n            \"inherit\",\n            \"primary\",\n            \"secondary\",\n            \"error\",\n            \"info\",\n            \"success\",\n            \"warning\"\n        ]),\n        (prop_types__WEBPACK_IMPORTED_MODULE_12___default().string)\n    ]),\n    /**\n   * If `true`, the shrink animation is disabled.\n   * This only works if variant is `indeterminate`.\n   * @default false\n   */ disableShrink: (0,_mui_utils_chainPropTypes__WEBPACK_IMPORTED_MODULE_13__[\"default\"])((prop_types__WEBPACK_IMPORTED_MODULE_12___default().bool), (props)=>{\n        if (props.disableShrink && props.variant && props.variant !== \"indeterminate\") {\n            return new Error(\"MUI: You have provided the `disableShrink` prop \" + \"with a variant other than `indeterminate`. This will have no effect.\");\n        }\n        return null;\n    }),\n    /**\n   * The size of the component.\n   * If using a number, the pixel unit is assumed.\n   * If using a string, you need to provide the CSS unit, for example '3rem'.\n   * @default 40\n   */ size: prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOfType([\n        (prop_types__WEBPACK_IMPORTED_MODULE_12___default().number),\n        (prop_types__WEBPACK_IMPORTED_MODULE_12___default().string)\n    ]),\n    /**\n   * @ignore\n   */ style: (prop_types__WEBPACK_IMPORTED_MODULE_12___default().object),\n    /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */ sx: prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOfType([\n        prop_types__WEBPACK_IMPORTED_MODULE_12___default().arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOfType([\n            (prop_types__WEBPACK_IMPORTED_MODULE_12___default().func),\n            (prop_types__WEBPACK_IMPORTED_MODULE_12___default().object),\n            (prop_types__WEBPACK_IMPORTED_MODULE_12___default().bool)\n        ])),\n        (prop_types__WEBPACK_IMPORTED_MODULE_12___default().func),\n        (prop_types__WEBPACK_IMPORTED_MODULE_12___default().object)\n    ]),\n    /**\n   * The thickness of the circle.\n   * @default 3.6\n   */ thickness: (prop_types__WEBPACK_IMPORTED_MODULE_12___default().number),\n    /**\n   * The value of the progress indicator for the determinate variant.\n   * Value between 0 and 100.\n   * @default 0\n   */ value: (prop_types__WEBPACK_IMPORTED_MODULE_12___default().number),\n    /**\n   * The variant to use.\n   * Use indeterminate when there is no progress value.\n   * @default 'indeterminate'\n   */ variant: prop_types__WEBPACK_IMPORTED_MODULE_12___default().oneOf([\n        \"determinate\",\n        \"indeterminate\"\n    ])\n} : 0;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CircularProgress);\nvar _c, _c1;\n$RefreshReg$(_c, \"CircularProgress$React.forwardRef\");\n$RefreshReg$(_c1, \"CircularProgress\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@mui/material/CircularProgress/circularProgressClasses.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@mui/material/CircularProgress/circularProgressClasses.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCircularProgressUtilityClass: function() { return /* binding */ getCircularProgressUtilityClass; }\n/* harmony export */ });\n/* harmony import */ var _mui_utils_generateUtilityClasses__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mui/utils/generateUtilityClasses */ \"(app-pages-browser)/./node_modules/@mui/utils/generateUtilityClasses/generateUtilityClasses.js\");\n/* harmony import */ var _mui_utils_generateUtilityClass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/utils/generateUtilityClass */ \"(app-pages-browser)/./node_modules/@mui/utils/generateUtilityClass/generateUtilityClass.js\");\n\n\nfunction getCircularProgressUtilityClass(slot) {\n    return (0,_mui_utils_generateUtilityClass__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MuiCircularProgress\", slot);\n}\nconst circularProgressClasses = (0,_mui_utils_generateUtilityClasses__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"MuiCircularProgress\", [\n    \"root\",\n    \"determinate\",\n    \"indeterminate\",\n    \"colorPrimary\",\n    \"colorSecondary\",\n    \"svg\",\n    \"circle\",\n    \"circleDeterminate\",\n    \"circleIndeterminate\",\n    \"circleDisableShrink\"\n]);\n/* harmony default export */ __webpack_exports__[\"default\"] = (circularProgressClasses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL0NpcmN1bGFyUHJvZ3Jlc3MvY2lyY3VsYXJQcm9ncmVzc0NsYXNzZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVFO0FBQ0o7QUFDNUQsU0FBU0UsZ0NBQWdDQyxJQUFJO0lBQ2xELE9BQU9GLDJFQUFvQkEsQ0FBQyx1QkFBdUJFO0FBQ3JEO0FBQ0EsTUFBTUMsMEJBQTBCSiw2RUFBc0JBLENBQUMsdUJBQXVCO0lBQUM7SUFBUTtJQUFlO0lBQWlCO0lBQWdCO0lBQWtCO0lBQU87SUFBVTtJQUFxQjtJQUF1QjtDQUFzQjtBQUM1TywrREFBZUksdUJBQXVCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL0NpcmN1bGFyUHJvZ3Jlc3MvY2lyY3VsYXJQcm9ncmVzc0NsYXNzZXMuanM/NzAyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2VuZXJhdGVVdGlsaXR5Q2xhc3NlcyBmcm9tICdAbXVpL3V0aWxzL2dlbmVyYXRlVXRpbGl0eUNsYXNzZXMnO1xuaW1wb3J0IGdlbmVyYXRlVXRpbGl0eUNsYXNzIGZyb20gJ0BtdWkvdXRpbHMvZ2VuZXJhdGVVdGlsaXR5Q2xhc3MnO1xuZXhwb3J0IGZ1bmN0aW9uIGdldENpcmN1bGFyUHJvZ3Jlc3NVdGlsaXR5Q2xhc3Moc2xvdCkge1xuICByZXR1cm4gZ2VuZXJhdGVVdGlsaXR5Q2xhc3MoJ011aUNpcmN1bGFyUHJvZ3Jlc3MnLCBzbG90KTtcbn1cbmNvbnN0IGNpcmN1bGFyUHJvZ3Jlc3NDbGFzc2VzID0gZ2VuZXJhdGVVdGlsaXR5Q2xhc3NlcygnTXVpQ2lyY3VsYXJQcm9ncmVzcycsIFsncm9vdCcsICdkZXRlcm1pbmF0ZScsICdpbmRldGVybWluYXRlJywgJ2NvbG9yUHJpbWFyeScsICdjb2xvclNlY29uZGFyeScsICdzdmcnLCAnY2lyY2xlJywgJ2NpcmNsZURldGVybWluYXRlJywgJ2NpcmNsZUluZGV0ZXJtaW5hdGUnLCAnY2lyY2xlRGlzYWJsZVNocmluayddKTtcbmV4cG9ydCBkZWZhdWx0IGNpcmN1bGFyUHJvZ3Jlc3NDbGFzc2VzOyJdLCJuYW1lcyI6WyJnZW5lcmF0ZVV0aWxpdHlDbGFzc2VzIiwiZ2VuZXJhdGVVdGlsaXR5Q2xhc3MiLCJnZXRDaXJjdWxhclByb2dyZXNzVXRpbGl0eUNsYXNzIiwic2xvdCIsImNpcmN1bGFyUHJvZ3Jlc3NDbGFzc2VzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/material/CircularProgress/circularProgressClasses.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx":
/*!**********************************************!*\
  !*** ./src/app/(DashboardLayout)/layout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Container,styled!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/header/Header */ \"(app-pages-browser)/./src/app/(DashboardLayout)/layout/header/Header.tsx\");\n/* harmony import */ var _app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/sidebar/Sidebar */ \"(app-pages-browser)/./src/app/(DashboardLayout)/layout/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst MainWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\")(()=>({\n        display: \"flex\",\n        minHeight: \"100vh\",\n        width: \"100%\"\n    }));\n_c = MainWrapper;\nconst PageWrapper = (0,_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\", {\n    shouldForwardProp: (prop)=>prop !== \"isSidebarOpen\"\n})((param)=>{\n    let { theme, isSidebarOpen } = param;\n    return {\n        display: \"flex\",\n        flexGrow: 1,\n        paddingBottom: \"60px\",\n        flexDirection: \"column\",\n        zIndex: 1,\n        backgroundColor: \"transparent\",\n        marginLeft: \"0px\",\n        transition: \"all 0.2s ease-in-out\",\n        [theme.breakpoints.up(\"lg\")]: {\n            marginLeft: isSidebarOpen ? \"270px\" : \"0px\"\n        }\n    };\n});\n_c1 = PageWrapper;\nfunction RootLayout(param) {\n    let { children } = param;\n    _s();\n    const [isSidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobileSidebarOpen, setMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!isSidebarOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainWrapper, {\n                className: \"mainwrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        isSidebarOpen: isSidebarOpen,\n                        isMobileSidebarOpen: isMobileSidebarOpen,\n                        onSidebarClose: ()=>setMobileSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageWrapper, {\n                        className: \"page-wrapper\",\n                        isSidebarOpen: isSidebarOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_header_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                toggleMobileSidebar: ()=>setMobileSidebarOpen(true),\n                                toggleSidebar: toggleSidebar,\n                                isSidebarOpen: isSidebarOpen\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                sx: {\n                                    paddingTop: \"20px\",\n                                    maxWidth: \"1200px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Container_styled_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        minHeight: \"calc(100vh - 170px)\"\n                                    },\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            \"// \"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(RootLayout, \"Vb6bppI4DuiAlAQexSsBaC+YFw0=\");\n_c2 = RootLayout;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MainWrapper\");\n$RefreshReg$(_c1, \"PageWrapper\");\n$RefreshReg$(_c2, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProtectedRoute; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Define restricted routes for different roles\nconst restrictedRoutes = {\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\"\n    ],\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.TEACHER]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\",\n        \"/class-management/students\"\n    ],\n    [_prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.STUDENT]: [\n        \"/\",\n        \"/class-management/teachers\",\n        \"/class-management/payments\",\n        \"/class-management/students\",\n        \"/class-management/classes\",\n        \"/class-management/cabinets\",\n        \"/class-management/timetable\"\n    ]\n};\nfunction ProtectedRoute(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) return; // Wait for auth check to complete\n        if (!isAuthenticated && pathname !== \"/authentication/login\") {\n            router.push(\"/authentication/login\");\n            return;\n        }\n        // Check if user has access to current route\n        if (isAuthenticated && user) {\n            const userRestrictedRoutes = restrictedRoutes[user.role] || [];\n            const isRestricted = userRestrictedRoutes.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n            if (isRestricted) {\n                // Redirect to appropriate page based on role\n                const redirectUrl = getRedirectUrlForRole(user.role);\n                router.push(redirectUrl);\n            }\n        }\n    }, [\n        pathname,\n        router,\n        isAuthenticated,\n        user,\n        isLoading\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!isAuthenticated && pathname !== \"/authentication/login\") {\n        return null;\n    }\n    // Don't render restricted pages\n    if (isAuthenticated && user) {\n        const userRestrictedRoutes = restrictedRoutes[user.role] || [];\n        const isRestricted = userRestrictedRoutes.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n        if (isRestricted) {\n            return null;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"a88Bbxcn6r0gYcf1M7DK/9R27wo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProtectedRoute;\nfunction getRedirectUrlForRole(role) {\n    switch(role){\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.ADMIN:\n            return \"/\"; // Dashboard\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.RECEPTION:\n            return \"/class-management/classes\"; // Classes page\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.TEACHER:\n            return \"/class-management/classes\"; // Classes page\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.STUDENT:\n            return \"/student/dashboard\"; // Student dashboard (to be implemented)\n        default:\n            return \"/authentication/login\";\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\n"));

/***/ })

});