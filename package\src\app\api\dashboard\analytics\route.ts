import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Admin and Reception can view analytics
    if (!AuthService.hasPermission(userRole, UserRole.RECEPTION)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get date range for analytics (last 30 days by default)
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Parallel queries for better performance
    const [
      totalStudents,
      totalTeachers,
      totalClasses,
      activeClasses,
      totalPayments,
      pendingPayments,
      recentEnrollments,
      attendanceStats,
      gradeStats,
      paymentStats,
      studentStatusDistribution,
      classCapacityStats,
    ] = await Promise.all([
      // Total students
      prisma.student.count(),
      
      // Total teachers
      prisma.teacher.count(),
      
      // Total classes
      prisma.class.count(),
      
      // Active classes
      prisma.class.count({
        where: { status: 'ACTIVE' }
      }),
      
      // Total payments amount
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: { status: 'COMPLETED' }
      }),
      
      // Pending payments amount
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: { status: 'PENDING' }
      }),
      
      // Recent enrollments (last 30 days)
      prisma.student.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),
      
      // Attendance statistics
      prisma.attendance.groupBy({
        by: ['status'],
        _count: { status: true },
        where: {
          date: { gte: startDate }
        }
      }),
      
      // Grade statistics
      prisma.grade.aggregate({
        _avg: { percentage: true },
        _count: { id: true },
        where: {
          date: { gte: startDate }
        }
      }),
      
      // Payment statistics by month
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', date) as month,
          SUM(amount) as total_amount,
          COUNT(*) as payment_count
        FROM "Payment" 
        WHERE date >= ${startDate}
        GROUP BY DATE_TRUNC('month', date)
        ORDER BY month DESC
      `,
      
      // Student status distribution
      prisma.student.groupBy({
        by: ['status'],
        _count: { status: true }
      }),
      
      // Class capacity utilization
      prisma.$queryRaw`
        SELECT 
          c.id,
          c.name,
          c."maxStudents",
          COUNT(s.id) as current_students,
          ROUND((COUNT(s.id)::float / c."maxStudents") * 100, 2) as utilization_percentage
        FROM "Class" c
        LEFT JOIN "_ClassToStudent" cs ON c.id = cs."A"
        LEFT JOIN "Student" s ON cs."B" = s.id
        WHERE c.status = 'ACTIVE'
        GROUP BY c.id, c.name, c."maxStudents"
        ORDER BY utilization_percentage DESC
      `,
    ]);

    // Calculate additional metrics
    const totalRevenue = totalPayments._sum.amount || 0;
    const pendingRevenue = pendingPayments._sum.amount || 0;
    const averageGrade = gradeStats._avg.percentage || 0;
    const totalGrades = gradeStats._count || 0;

    // Process attendance statistics
    const attendanceByStatus = attendanceStats.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {} as Record<string, number>);

    const totalAttendanceRecords = attendanceStats.reduce((sum, item) => sum + item._count.status, 0);
    const attendanceRate = totalAttendanceRecords > 0 
      ? Math.round(((attendanceByStatus.PRESENT || 0) / totalAttendanceRecords) * 100)
      : 0;

    // Process student status distribution
    const studentsByStatus = studentStatusDistribution.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {} as Record<string, number>);

    // Calculate growth metrics (comparing with previous period)
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - days);
    
    const [previousEnrollments, previousPayments] = await Promise.all([
      prisma.student.count({
        where: {
          createdAt: {
            gte: previousPeriodStart,
            lt: startDate
          }
        }
      }),
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: {
          status: 'COMPLETED',
          date: {
            gte: previousPeriodStart,
            lt: startDate
          }
        }
      })
    ]);

    const enrollmentGrowth = previousEnrollments > 0 
      ? Math.round(((recentEnrollments - previousEnrollments) / previousEnrollments) * 100)
      : 0;

    const revenueGrowth = (previousPayments._sum.amount || 0) > 0
      ? Math.round(((totalRevenue - (previousPayments._sum.amount || 0)) / (previousPayments._sum.amount || 0)) * 100)
      : 0;

    const analytics = {
      overview: {
        totalStudents,
        totalTeachers,
        totalClasses,
        activeClasses,
        totalRevenue,
        pendingRevenue,
        recentEnrollments,
        enrollmentGrowth,
        revenueGrowth,
      },
      attendance: {
        totalRecords: totalAttendanceRecords,
        attendanceRate,
        byStatus: attendanceByStatus,
      },
      grades: {
        totalGrades,
        averageGrade: Math.round(averageGrade),
      },
      students: {
        byStatus: studentsByStatus,
        total: totalStudents,
      },
      payments: {
        monthly: paymentStats,
        totalRevenue,
        pendingRevenue,
      },
      classes: {
        capacity: classCapacityStats,
        total: totalClasses,
        active: activeClasses,
      },
    };

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'READ',
      'Dashboard',
      'analytics',
      null,
      { period: `${days} days` },
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Get dashboard analytics error:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 });
  }
}
