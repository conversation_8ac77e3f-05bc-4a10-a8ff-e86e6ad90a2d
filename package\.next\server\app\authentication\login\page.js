/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/authentication/login/page";
exports.ids = ["app/authentication/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "@prisma/client?5f3f":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauthentication%2Flogin%2Fpage&page=%2Fauthentication%2Flogin%2Fpage&appPaths=%2Fauthentication%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauthentication%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauthentication%2Flogin%2Fpage&page=%2Fauthentication%2Flogin%2Fpage&appPaths=%2Fauthentication%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauthentication%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'authentication',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/authentication/login/page.tsx */ \"(rsc)/./src/app/authentication/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/authentication/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/authentication/login/page\",\n        pathname: \"/authentication/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauthentication%2Flogin%2Fpage&page=%2Fauthentication%2Flogin%2Fpage&appPaths=%2Fauthentication%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauthentication%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Cauthentication%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Cauthentication%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/authentication/login/page.tsx */ \"(ssr)/./src/app/authentication/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlcyU1QyU1Q0ltcHJvdmVkLUNSTSU1QyU1Q3BhY2thZ2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoZW50aWNhdGlvbiU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUE0SSIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybml6ZS1uZXh0LWZyZWUvP2ZkOWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERlc2t0b3BcXFxcY29kZXNcXFxcSW1wcm92ZWQtQ1JNXFxcXHBhY2thZ2VcXFxcc3JjXFxcXGFwcFxcXFxhdXRoZW50aWNhdGlvblxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Cauthentication%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0Rlc2t0b3AlNUMlNUNjb2RlcyU1QyU1Q0ltcHJvdmVkLUNSTSU1QyU1Q3BhY2thZ2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBdUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLz9lOTA0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXEltcHJvdmVkLUNSTVxcXFxwYWNrYWdlXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CImproved-CRM%5C%5Cpackage%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/(DashboardLayout)/components/container/PageContainer.tsx ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_helmet_async__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-helmet-async */ \"(ssr)/./node_modules/react-helmet-async/lib/index.module.js\");\n// import { Helmet } from 'react-helmet';\n\n\nconst PageContainer = ({ title, description, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_helmet_async__WEBPACK_IMPORTED_MODULE_1__.HelmetProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_helmet_async__WEBPACK_IMPORTED_MODULE_1__.Helmet, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\container\\\\PageContainer.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhEYXNoYm9hcmRMYXlvdXQpL2NvbXBvbmVudHMvY29udGFpbmVyL1BhZ2VDb250YWluZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx5Q0FBeUM7O0FBQ21CO0FBUzVELE1BQU1FLGdCQUFnQixDQUFDLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxRQUFRLEVBQVMsaUJBQzVELDhEQUFDSiw4REFBY0E7a0JBQ2IsNEVBQUNLOzs4QkFDQyw4REFBQ04sc0RBQU1BOztzQ0FDTCw4REFBQ0c7c0NBQU9BOzs7Ozs7c0NBQ1IsOERBQUNJOzRCQUFLQyxNQUFLOzRCQUFjQyxTQUFTTDs7Ozs7Ozs7Ozs7O2dCQUVuQ0M7Ozs7Ozs7Ozs7OztBQUtQLGlFQUFlSCxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9hcHAvKERhc2hib2FyZExheW91dCkvY29tcG9uZW50cy9jb250YWluZXIvUGFnZUNvbnRhaW5lci50c3g/YTRlYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpbXBvcnQgeyBIZWxtZXQgfSBmcm9tICdyZWFjdC1oZWxtZXQnO1xyXG5pbXBvcnQgeyBIZWxtZXQsIEhlbG1ldFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtaGVsbWV0LWFzeW5jJztcclxuXHJcblxyXG50eXBlIFByb3BzID0ge1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIGNoaWxkcmVuOiBKU1guRWxlbWVudCB8IEpTWC5FbGVtZW50W107XHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbn07XHJcblxyXG5jb25zdCBQYWdlQ29udGFpbmVyID0gKHsgdGl0bGUsIGRlc2NyaXB0aW9uLCBjaGlsZHJlbiB9OiBQcm9wcykgPT4gKFxyXG4gIDxIZWxtZXRQcm92aWRlcj5cclxuICAgIDxkaXY+XHJcbiAgICAgIDxIZWxtZXQ+XHJcbiAgICAgICAgPHRpdGxlPnt0aXRsZX08L3RpdGxlPlxyXG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e2Rlc2NyaXB0aW9ufSAvPlxyXG4gICAgICA8L0hlbG1ldD5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9kaXY+XHJcbiAgPC9IZWxtZXRQcm92aWRlcj5cclxuKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFBhZ2VDb250YWluZXI7XHJcbiJdLCJuYW1lcyI6WyJIZWxtZXQiLCJIZWxtZXRQcm92aWRlciIsIlBhZ2VDb250YWluZXIiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY2hpbGRyZW4iLCJkaXYiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField.tsx":
/*!***************************************************************************************!*\
  !*** ./src/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField.tsx ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n\n\n\n\nconst CustomTextField = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\components\\\\forms\\\\theme-elements\\\\CustomTextField.tsx\",\n        lineNumber: 5,\n        columnNumber: 48\n    }, undefined))(({ theme })=>({\n        \"& .MuiOutlinedInput-input::-webkit-input-placeholder\": {\n            color: theme.palette.text.secondary,\n            opacity: \"0.8\"\n        },\n        \"& .MuiOutlinedInput-input.Mui-disabled::-webkit-input-placeholder\": {\n            color: theme.palette.text.secondary,\n            opacity: \"1\"\n        },\n        \"& .Mui-disabled .MuiOutlinedInput-notchedOutline\": {\n            borderColor: theme.palette.grey[200]\n        }\n    }));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomTextField);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhEYXNoYm9hcmRMYXlvdXQpL2NvbXBvbmVudHMvZm9ybXMvdGhlbWUtZWxlbWVudHMvQ3VzdG9tVGV4dEZpZWxkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNvQjtBQUNKO0FBRTFDLE1BQU1HLGtCQUFrQkYsZ0VBQU1BLENBQUMsQ0FBQ0csc0JBQWUsOERBQUNGLHFGQUFTQTtRQUFFLEdBQUdFLEtBQUs7Ozs7O21CQUFNLENBQUMsRUFBRUMsS0FBSyxFQUFFLEdBQU07UUFDdkYsd0RBQXdEO1lBQ3REQyxPQUFPRCxNQUFNRSxPQUFPLENBQUNDLElBQUksQ0FBQ0MsU0FBUztZQUNuQ0MsU0FBUztRQUNYO1FBQ0EscUVBQXFFO1lBQ25FSixPQUFPRCxNQUFNRSxPQUFPLENBQUNDLElBQUksQ0FBQ0MsU0FBUztZQUNuQ0MsU0FBUztRQUNYO1FBQ0Esb0RBQW9EO1lBQ2xEQyxhQUFhTixNQUFNRSxPQUFPLENBQUNLLElBQUksQ0FBQyxJQUFJO1FBQ3RDO0lBQ0Y7QUFFQSxpRUFBZVQsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybml6ZS1uZXh0LWZyZWUvLi9zcmMvYXBwLyhEYXNoYm9hcmRMYXlvdXQpL2NvbXBvbmVudHMvZm9ybXMvdGhlbWUtZWxlbWVudHMvQ3VzdG9tVGV4dEZpZWxkLnRzeD9iMzU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHN0eWxlZCB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwvc3R5bGVzJztcclxuaW1wb3J0IHsgVGV4dEZpZWxkIH0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XHJcblxyXG5jb25zdCBDdXN0b21UZXh0RmllbGQgPSBzdHlsZWQoKHByb3BzOiBhbnkpID0+IDxUZXh0RmllbGQgey4uLnByb3BzfSAvPikoKHsgdGhlbWUgfSkgPT4gKHtcclxuICAnJiAuTXVpT3V0bGluZWRJbnB1dC1pbnB1dDo6LXdlYmtpdC1pbnB1dC1wbGFjZWhvbGRlcic6IHtcclxuICAgIGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQuc2Vjb25kYXJ5LFxyXG4gICAgb3BhY2l0eTogJzAuOCcsXHJcbiAgfSxcclxuICAnJiAuTXVpT3V0bGluZWRJbnB1dC1pbnB1dC5NdWktZGlzYWJsZWQ6Oi13ZWJraXQtaW5wdXQtcGxhY2Vob2xkZXInOiB7XHJcbiAgICBjb2xvcjogdGhlbWUucGFsZXR0ZS50ZXh0LnNlY29uZGFyeSxcclxuICAgIG9wYWNpdHk6ICcxJyxcclxuICB9LFxyXG4gICcmIC5NdWktZGlzYWJsZWQgLk11aU91dGxpbmVkSW5wdXQtbm90Y2hlZE91dGxpbmUnOiB7XHJcbiAgICBib3JkZXJDb2xvcjogdGhlbWUucGFsZXR0ZS5ncmV5WzIwMF0sXHJcbiAgfSxcclxufSkpO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ3VzdG9tVGV4dEZpZWxkO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJzdHlsZWQiLCJUZXh0RmllbGQiLCJDdXN0b21UZXh0RmllbGQiLCJwcm9wcyIsInRoZW1lIiwiY29sb3IiLCJwYWxldHRlIiwidGV4dCIsInNlY29uZGFyeSIsIm9wYWNpdHkiLCJib3JkZXJDb2xvciIsImdyZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(DashboardLayout)/layout/shared/logo/Logo.tsx":
/*!***************************************************************!*\
  !*** ./src/app/(DashboardLayout)/layout/shared/logo/Logo.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_styled_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Typography,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_styled_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Typography,styled!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n\n\n\nconst LinkStyled = (0,_barrel_optimize_names_Typography_styled_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>({\n        textDecoration: \"none\",\n        display: \"flex\",\n        alignItems: \"center\"\n    }));\nconst Logo = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkStyled, {\n        href: \"/\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_styled_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            variant: \"h4\",\n            color: \"primary\",\n            sx: {\n                fontWeight: 700,\n                lineHeight: 1.2\n            },\n            children: [\n                \"Innovative\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_styled_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    component: \"span\",\n                    variant: \"h4\",\n                    sx: {\n                        color: \"text.secondary\",\n                        fontWeight: 700,\n                        display: \"block\"\n                    },\n                    children: \"Centre\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\shared\\\\logo\\\\Logo.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\shared\\\\logo\\\\Logo.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\(DashboardLayout)\\\\layout\\\\shared\\\\logo\\\\Logo.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(DashboardLayout)/layout/shared/logo/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/authentication/auth/AuthLogin.tsx":
/*!***************************************************!*\
  !*** ./src/app/authentication/auth/AuthLogin.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Checkbox/Checkbox.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Checkbox,CircularProgress,FormControlLabel,FormGroup,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField */ \"(ssr)/./src/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField.tsx\");\n\n\n\n\n\n\nconst AuthLogin = ({ title, subtitle, subtext })=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // const { login } = useAuth();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        if (!email || !password) {\n            setError(\"Please enter both email and password\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // Direct API call for testing\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                router.push(\"/\");\n            } else {\n                setError(data.error || \"Login failed\");\n            }\n        } catch (error) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                fontWeight: \"700\",\n                variant: \"h2\",\n                mb: 1,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined) : null,\n            subtext,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 2\n                                },\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"subtitle1\",\n                                        fontWeight: 600,\n                                        component: \"label\",\n                                        htmlFor: \"email\",\n                                        mb: \"5px\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        id: \"email\",\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                mt: \"25px\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"subtitle1\",\n                                        fontWeight: 600,\n                                        component: \"label\",\n                                        htmlFor: \"password\",\n                                        mb: \"5px\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_forms_theme_elements_CustomTextField__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        id: \"password\",\n                                        type: \"password\",\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        value: password,\n                                        onChange: (e)=>setPassword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                justifyContent: \"space-between\",\n                                direction: \"row\",\n                                alignItems: \"center\",\n                                my: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                defaultChecked: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 26\n                                            }, void 0),\n                                            label: \"Remember this Device\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        component: next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                                        href: \"/\",\n                                        fontWeight: \"500\",\n                                        sx: {\n                                            textDecoration: \"none\",\n                                            color: \"primary.main\"\n                                        },\n                                        children: \"Forgot Password ?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            color: \"primary\",\n                            variant: \"contained\",\n                            size: \"large\",\n                            fullWidth: true,\n                            type: \"submit\",\n                            disabled: isLoading,\n                            startIcon: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Checkbox_CircularProgress_FormControlLabel_FormGroup_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                color: \"inherit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 36\n                            }, void 0) : null,\n                            children: isLoading ? \"Signing In...\" : \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\auth\\\\AuthLogin.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            subtitle\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthLogin);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/authentication/auth/AuthLogin.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/authentication/login/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/authentication/login/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,Grid,Stack,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _app_DashboardLayout_components_container_PageContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/(DashboardLayout)/components/container/PageContainer */ \"(ssr)/./src/app/(DashboardLayout)/components/container/PageContainer.tsx\");\n/* harmony import */ var _app_DashboardLayout_layout_shared_logo_Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/(DashboardLayout)/layout/shared/logo/Logo */ \"(ssr)/./src/app/(DashboardLayout)/layout/shared/logo/Logo.tsx\");\n/* harmony import */ var _auth_AuthLogin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/AuthLogin */ \"(ssr)/./src/app/authentication/auth/AuthLogin.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// components\n\n\n\nconst Login2 = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_components_container_PageContainer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"Login\",\n        description: \"this is Login page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                position: \"relative\",\n                \"&:before\": {\n                    content: '\"\"',\n                    background: \"radial-gradient(#d2f1df, #d3d7fa, #bad8f4)\",\n                    backgroundSize: \"400% 400%\",\n                    animation: \"gradient 15s ease infinite\",\n                    position: \"absolute\",\n                    height: \"100%\",\n                    width: \"100%\",\n                    opacity: \"0.3\"\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                container: true,\n                spacing: 0,\n                justifyContent: \"center\",\n                sx: {\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    item: true,\n                    xs: 12,\n                    sm: 12,\n                    lg: 4,\n                    xl: 3,\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        elevation: 9,\n                        sx: {\n                            p: 4,\n                            zIndex: 1,\n                            width: \"100%\",\n                            maxWidth: \"500px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_DashboardLayout_layout_shared_logo_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_AuthLogin__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                subtext: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"subtitle1\",\n                                    textAlign: \"center\",\n                                    color: \"textSecondary\",\n                                    mb: 1,\n                                    children: \"Welcome to Innovative Centre\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 19\n                                }, void 0),\n                                subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    direction: \"row\",\n                                    spacing: 1,\n                                    justifyContent: \"center\",\n                                    mt: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            color: \"textSecondary\",\n                                            variant: \"h6\",\n                                            fontWeight: \"500\",\n                                            children: \"New to Innovative Centre?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_Grid_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            component: next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n                                            href: \"/authentication/register\",\n                                            fontWeight: \"500\",\n                                            sx: {\n                                                textDecoration: \"none\",\n                                                color: \"primary.main\"\n                                            },\n                                            children: \"Create an account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\authentication\\\\login\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Login2);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/authentication/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/theme/DefaultColors */ \"(ssr)/./src/utils/theme/DefaultColors.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction LayoutContent({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isAuthPage = pathname?.startsWith(\"/authentication/\");\n    if (isAuthPage) {\n        // Don't wrap authentication pages with AuthProvider\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Wrap other pages with AuthProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                theme: _utils_theme_DefaultColors__WEBPACK_IMPORTED_MODULE_1__.baselightTheme,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContent, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?5f3f\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated\n    const isAuthenticated = !!user;\n    // Role hierarchy for permission checking\n    const roleHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.STUDENT]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.TEACHER]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.RECEPTION]: 2,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN]: 3\n    };\n    // Check if user has required permission\n    const hasPermission = (requiredRole)=>{\n        if (!user) return false;\n        return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n    };\n    // Login function\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setUser(data.user);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error occurred\"\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            // Redirect to login page using Next.js navigation\n            if (false) {}\n        }\n    };\n    // Refresh token function\n    const refreshToken = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.user);\n                    return true;\n                }\n            }\n            // If refresh fails, logout user\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Get current user on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getCurrentUser = async ()=>{\n            try {\n                const response = await fetch(\"/api/auth/me\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    }\n                } else if (response.status === 401) {\n                    // Try to refresh token\n                    const refreshSuccess = await refreshToken();\n                    if (!refreshSuccess) {\n                        // Don't redirect here, let ProtectedRoute handle it\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Get current user error:\", error);\n                setUser(null);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getCurrentUser();\n    }, []);\n    // Auto-refresh token before expiry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(async ()=>{\n            await refreshToken();\n        }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/theme/DefaultColors.tsx":
/*!*******************************************!*\
  !*** ./src/utils/theme/DefaultColors.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baselightTheme: () => (/* binding */ baselightTheme),\n/* harmony export */   plus: () => (/* reexport default from dynamic */ next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\utils\\\\theme\\\\DefaultColors.tsx\",\"import\":\"Plus_Jakarta_Sans\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"display\":\"swap\",\"fallback\":[\"Helvetica\",\"Arial\",\"sans-serif\"]}],\"variableName\":\"plus\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\utils\\\\\\\\theme\\\\\\\\DefaultColors.tsx\\\",\\\"import\\\":\\\"Plus_Jakarta_Sans\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"fallback\\\":[\\\"Helvetica\\\",\\\"Arial\\\",\\\"sans-serif\\\"]}],\\\"variableName\\\":\\\"plus\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n\n\nconst baselightTheme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    direction: \"ltr\",\n    palette: {\n        primary: {\n            main: \"#5D87FF\",\n            light: \"#ECF2FF\",\n            dark: \"#4570EA\"\n        },\n        secondary: {\n            main: \"#49BEFF\",\n            light: \"#E8F7FF\",\n            dark: \"#23afdb\"\n        },\n        success: {\n            main: \"#13DEB9\",\n            light: \"#E6FFFA\",\n            dark: \"#02b3a9\",\n            contrastText: \"#ffffff\"\n        },\n        info: {\n            main: \"#539BFF\",\n            light: \"#EBF3FE\",\n            dark: \"#1682d4\",\n            contrastText: \"#ffffff\"\n        },\n        error: {\n            main: \"#FA896B\",\n            light: \"#FDEDE8\",\n            dark: \"#f3704d\",\n            contrastText: \"#ffffff\"\n        },\n        warning: {\n            main: \"#FFAE1F\",\n            light: \"#FEF5E5\",\n            dark: \"#ae8e59\",\n            contrastText: \"#ffffff\"\n        },\n        grey: {\n            100: \"#F2F6FA\",\n            200: \"#EAEFF4\",\n            300: \"#DFE5EF\",\n            400: \"#7C8FAC\",\n            500: \"#5A6A85\",\n            600: \"#2A3547\"\n        },\n        text: {\n            primary: \"#2A3547\",\n            secondary: \"#5A6A85\"\n        },\n        action: {\n            disabledBackground: \"rgba(73,82,88,0.12)\",\n            hoverOpacity: 0.02,\n            hover: \"#f6f9fc\"\n        },\n        divider: \"#e5eaef\"\n    },\n    typography: {\n        fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily,\n        h1: {\n            fontWeight: 600,\n            fontSize: \"2.25rem\",\n            lineHeight: \"2.75rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h2: {\n            fontWeight: 600,\n            fontSize: \"1.875rem\",\n            lineHeight: \"2.25rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h3: {\n            fontWeight: 600,\n            fontSize: \"1.5rem\",\n            lineHeight: \"1.75rem\",\n            fontFamily: (next_font_google_target_css_path_src_utils_theme_DefaultColors_tsx_import_Plus_Jakarta_Sans_arguments_weight_300_400_500_600_700_subsets_latin_display_swap_fallback_Helvetica_Arial_sans_serif_variableName_plus___WEBPACK_IMPORTED_MODULE_1___default().style).fontFamily\n        },\n        h4: {\n            fontWeight: 600,\n            fontSize: \"1.3125rem\",\n            lineHeight: \"1.6rem\"\n        },\n        h5: {\n            fontWeight: 600,\n            fontSize: \"1.125rem\",\n            lineHeight: \"1.6rem\"\n        },\n        h6: {\n            fontWeight: 600,\n            fontSize: \"1rem\",\n            lineHeight: \"1.2rem\"\n        },\n        button: {\n            textTransform: \"capitalize\",\n            fontWeight: 400\n        },\n        body1: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400,\n            lineHeight: \"1.334rem\"\n        },\n        body2: {\n            fontSize: \"0.75rem\",\n            letterSpacing: \"0rem\",\n            fontWeight: 400,\n            lineHeight: \"1rem\"\n        },\n        subtitle1: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400\n        },\n        subtitle2: {\n            fontSize: \"0.875rem\",\n            fontWeight: 400\n        }\n    },\n    components: {\n        MuiCssBaseline: {\n            styleOverrides: {\n                \".MuiPaper-elevation9, .MuiPopover-root .MuiPaper-elevation\": {\n                    boxShadow: \"rgb(145 158 171 / 30%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 12px 24px -4px !important\"\n                }\n            }\n        },\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                    borderRadius: \"7px\"\n                }\n            }\n        }\n    }\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvdGhlbWUvRGVmYXVsdENvbG9ycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFHYUE7QUFIc0M7QUFVbkQsTUFBTUUsaUJBQWlCRCxnRUFBV0EsQ0FBQztJQUNqQ0UsV0FBVztJQUNYQyxTQUFTO1FBQ1BDLFNBQVM7WUFDUEMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxXQUFXO1lBQ1RILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUUsU0FBUztZQUNQSixNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNORyxjQUFjO1FBQ2hCO1FBQ0FDLE1BQU07WUFDSk4sTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkcsY0FBYztRQUNoQjtRQUNBRSxPQUFPO1lBQ0xQLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05HLGNBQWM7UUFDaEI7UUFDQUcsU0FBUztZQUNQUixNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNORyxjQUFjO1FBQ2hCO1FBQ0FJLE1BQU07WUFDSixLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7UUFDUDtRQUNBQyxNQUFNO1lBQ0pYLFNBQVM7WUFDVEksV0FBVztRQUNiO1FBQ0FRLFFBQVE7WUFDTkMsb0JBQW9CO1lBQ3BCQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtRQUNBQyxTQUFTO0lBQ1g7SUFDQUMsWUFBWTtRQUNWQyxZQUFZdkIsaVFBQVUsQ0FBQ3VCLFVBQVU7UUFDakNFLElBQUk7WUFDRkMsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FNLElBQUk7WUFDRkgsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FPLElBQUk7WUFDRkosWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkwsWUFBWXZCLGlRQUFVLENBQUN1QixVQUFVO1FBQ25DO1FBQ0FRLElBQUk7WUFDRkwsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFlBQVk7UUFDZDtRQUNBSSxJQUFJO1lBQ0ZOLFlBQVk7WUFDWkMsVUFBVTtZQUNWQyxZQUFZO1FBQ2Q7UUFDQUssSUFBSTtZQUNGUCxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsWUFBWTtRQUNkO1FBQ0FNLFFBQVE7WUFDTkMsZUFBZTtZQUNmVCxZQUFZO1FBQ2Q7UUFDQVUsT0FBTztZQUNMVCxVQUFVO1lBQ1ZELFlBQVk7WUFDWkUsWUFBWTtRQUNkO1FBQ0FTLE9BQU87WUFDTFYsVUFBVTtZQUNWVyxlQUFlO1lBQ2ZaLFlBQVk7WUFDWkUsWUFBWTtRQUNkO1FBQ0FXLFdBQVc7WUFDVFosVUFBVTtZQUNWRCxZQUFZO1FBQ2Q7UUFDQWMsV0FBVztZQUNUYixVQUFVO1lBQ1ZELFlBQVk7UUFDZDtJQUNGO0lBQ0FlLFlBQVk7UUFDVkMsZ0JBQWdCO1lBQ2RDLGdCQUFnQjtnQkFDZCw4REFBOEQ7b0JBQzVEQyxXQUNFO2dCQUNKO1lBQ0Y7UUFDRjtRQUNBQyxTQUFTO1lBQ1BGLGdCQUFnQjtnQkFDZEcsTUFBTTtvQkFDSkMsY0FBYztnQkFDaEI7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUUwQjtBQTdJYi9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy91dGlscy90aGVtZS9EZWZhdWx0Q29sb3JzLnRzeD9lMjM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVRoZW1lIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvc3R5bGVzXCI7XHJcbmltcG9ydCB7IFBsdXNfSmFrYXJ0YV9TYW5zIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBwbHVzID0gUGx1c19KYWthcnRhX1NhbnMoe1xyXG4gIHdlaWdodDogW1wiMzAwXCIsIFwiNDAwXCIsIFwiNTAwXCIsIFwiNjAwXCIsIFwiNzAwXCJdLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG4gIGRpc3BsYXk6IFwic3dhcFwiLFxyXG4gIGZhbGxiYWNrOiBbXCJIZWx2ZXRpY2FcIiwgXCJBcmlhbFwiLCBcInNhbnMtc2VyaWZcIl0sXHJcbn0pO1xyXG5cclxuY29uc3QgYmFzZWxpZ2h0VGhlbWUgPSBjcmVhdGVUaGVtZSh7XHJcbiAgZGlyZWN0aW9uOiBcImx0clwiLFxyXG4gIHBhbGV0dGU6IHtcclxuICAgIHByaW1hcnk6IHtcclxuICAgICAgbWFpbjogXCIjNUQ4N0ZGXCIsXHJcbiAgICAgIGxpZ2h0OiBcIiNFQ0YyRkZcIixcclxuICAgICAgZGFyazogXCIjNDU3MEVBXCIsXHJcbiAgICB9LFxyXG4gICAgc2Vjb25kYXJ5OiB7XHJcbiAgICAgIG1haW46IFwiIzQ5QkVGRlwiLFxyXG4gICAgICBsaWdodDogXCIjRThGN0ZGXCIsXHJcbiAgICAgIGRhcms6IFwiIzIzYWZkYlwiLFxyXG4gICAgfSxcclxuICAgIHN1Y2Nlc3M6IHtcclxuICAgICAgbWFpbjogXCIjMTNERUI5XCIsXHJcbiAgICAgIGxpZ2h0OiBcIiNFNkZGRkFcIixcclxuICAgICAgZGFyazogXCIjMDJiM2E5XCIsXHJcbiAgICAgIGNvbnRyYXN0VGV4dDogXCIjZmZmZmZmXCIsXHJcbiAgICB9LFxyXG4gICAgaW5mbzoge1xyXG4gICAgICBtYWluOiBcIiM1MzlCRkZcIixcclxuICAgICAgbGlnaHQ6IFwiI0VCRjNGRVwiLFxyXG4gICAgICBkYXJrOiBcIiMxNjgyZDRcIixcclxuICAgICAgY29udHJhc3RUZXh0OiBcIiNmZmZmZmZcIixcclxuICAgIH0sXHJcbiAgICBlcnJvcjoge1xyXG4gICAgICBtYWluOiBcIiNGQTg5NkJcIixcclxuICAgICAgbGlnaHQ6IFwiI0ZERURFOFwiLFxyXG4gICAgICBkYXJrOiBcIiNmMzcwNGRcIixcclxuICAgICAgY29udHJhc3RUZXh0OiBcIiNmZmZmZmZcIixcclxuICAgIH0sXHJcbiAgICB3YXJuaW5nOiB7XHJcbiAgICAgIG1haW46IFwiI0ZGQUUxRlwiLFxyXG4gICAgICBsaWdodDogXCIjRkVGNUU1XCIsXHJcbiAgICAgIGRhcms6IFwiI2FlOGU1OVwiLFxyXG4gICAgICBjb250cmFzdFRleHQ6IFwiI2ZmZmZmZlwiLFxyXG4gICAgfSxcclxuICAgIGdyZXk6IHtcclxuICAgICAgMTAwOiBcIiNGMkY2RkFcIixcclxuICAgICAgMjAwOiBcIiNFQUVGRjRcIixcclxuICAgICAgMzAwOiBcIiNERkU1RUZcIixcclxuICAgICAgNDAwOiBcIiM3QzhGQUNcIixcclxuICAgICAgNTAwOiBcIiM1QTZBODVcIixcclxuICAgICAgNjAwOiBcIiMyQTM1NDdcIixcclxuICAgIH0sXHJcbiAgICB0ZXh0OiB7XHJcbiAgICAgIHByaW1hcnk6IFwiIzJBMzU0N1wiLFxyXG4gICAgICBzZWNvbmRhcnk6IFwiIzVBNkE4NVwiLFxyXG4gICAgfSxcclxuICAgIGFjdGlvbjoge1xyXG4gICAgICBkaXNhYmxlZEJhY2tncm91bmQ6IFwicmdiYSg3Myw4Miw4OCwwLjEyKVwiLFxyXG4gICAgICBob3Zlck9wYWNpdHk6IDAuMDIsXHJcbiAgICAgIGhvdmVyOiBcIiNmNmY5ZmNcIixcclxuICAgIH0sXHJcbiAgICBkaXZpZGVyOiBcIiNlNWVhZWZcIixcclxuICB9LFxyXG4gIHR5cG9ncmFwaHk6IHtcclxuICAgIGZvbnRGYW1pbHk6IHBsdXMuc3R5bGUuZm9udEZhbWlseSxcclxuICAgIGgxOiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMi4yNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjIuNzVyZW1cIixcclxuICAgICAgZm9udEZhbWlseTogcGx1cy5zdHlsZS5mb250RmFtaWx5LFxyXG4gICAgfSxcclxuICAgIGgyOiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMS44NzVyZW1cIixcclxuICAgICAgbGluZUhlaWdodDogXCIyLjI1cmVtXCIsXHJcbiAgICAgIGZvbnRGYW1pbHk6IHBsdXMuc3R5bGUuZm9udEZhbWlseSxcclxuICAgIH0sXHJcbiAgICBoMzoge1xyXG4gICAgICBmb250V2VpZ2h0OiA2MDAsXHJcbiAgICAgIGZvbnRTaXplOiBcIjEuNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuNzVyZW1cIixcclxuICAgICAgZm9udEZhbWlseTogcGx1cy5zdHlsZS5mb250RmFtaWx5LFxyXG4gICAgfSxcclxuICAgIGg0OiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMS4zMTI1cmVtXCIsXHJcbiAgICAgIGxpbmVIZWlnaHQ6IFwiMS42cmVtXCIsXHJcbiAgICB9LFxyXG4gICAgaDU6IHtcclxuICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICBmb250U2l6ZTogXCIxLjEyNXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuNnJlbVwiLFxyXG4gICAgfSxcclxuICAgIGg2OiB7XHJcbiAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgZm9udFNpemU6IFwiMXJlbVwiLFxyXG4gICAgICBsaW5lSGVpZ2h0OiBcIjEuMnJlbVwiLFxyXG4gICAgfSxcclxuICAgIGJ1dHRvbjoge1xyXG4gICAgICB0ZXh0VHJhbnNmb3JtOiBcImNhcGl0YWxpemVcIixcclxuICAgICAgZm9udFdlaWdodDogNDAwLFxyXG4gICAgfSxcclxuICAgIGJvZHkxOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgICAgbGluZUhlaWdodDogXCIxLjMzNHJlbVwiLFxyXG4gICAgfSxcclxuICAgIGJvZHkyOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuNzVyZW1cIixcclxuICAgICAgbGV0dGVyU3BhY2luZzogXCIwcmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgICAgbGluZUhlaWdodDogXCIxcmVtXCIsXHJcbiAgICB9LFxyXG4gICAgc3VidGl0bGUxOiB7XHJcbiAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgIH0sXHJcbiAgICBzdWJ0aXRsZTI6IHtcclxuICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgZm9udFdlaWdodDogNDAwLFxyXG4gICAgfSxcclxuICB9LFxyXG4gIGNvbXBvbmVudHM6IHtcclxuICAgIE11aUNzc0Jhc2VsaW5lOiB7XHJcbiAgICAgIHN0eWxlT3ZlcnJpZGVzOiB7XHJcbiAgICAgICAgXCIuTXVpUGFwZXItZWxldmF0aW9uOSwgLk11aVBvcG92ZXItcm9vdCAuTXVpUGFwZXItZWxldmF0aW9uXCI6IHtcclxuICAgICAgICAgIGJveFNoYWRvdzpcclxuICAgICAgICAgICAgXCJyZ2IoMTQ1IDE1OCAxNzEgLyAzMCUpIDBweCAwcHggMnB4IDBweCwgcmdiKDE0NSAxNTggMTcxIC8gMTIlKSAwcHggMTJweCAyNHB4IC00cHggIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgTXVpQ2FyZDoge1xyXG4gICAgICBzdHlsZU92ZXJyaWRlczoge1xyXG4gICAgICAgIHJvb3Q6IHtcclxuICAgICAgICAgIGJvcmRlclJhZGl1czogXCI3cHhcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICB9LFxyXG59KTtcclxuXHJcbmV4cG9ydCB7IGJhc2VsaWdodFRoZW1lIH07XHJcbiJdLCJuYW1lcyI6WyJwbHVzIiwiY3JlYXRlVGhlbWUiLCJiYXNlbGlnaHRUaGVtZSIsImRpcmVjdGlvbiIsInBhbGV0dGUiLCJwcmltYXJ5IiwibWFpbiIsImxpZ2h0IiwiZGFyayIsInNlY29uZGFyeSIsInN1Y2Nlc3MiLCJjb250cmFzdFRleHQiLCJpbmZvIiwiZXJyb3IiLCJ3YXJuaW5nIiwiZ3JleSIsInRleHQiLCJhY3Rpb24iLCJkaXNhYmxlZEJhY2tncm91bmQiLCJob3Zlck9wYWNpdHkiLCJob3ZlciIsImRpdmlkZXIiLCJ0eXBvZ3JhcGh5IiwiZm9udEZhbWlseSIsInN0eWxlIiwiaDEiLCJmb250V2VpZ2h0IiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwiaDIiLCJoMyIsImg0IiwiaDUiLCJoNiIsImJ1dHRvbiIsInRleHRUcmFuc2Zvcm0iLCJib2R5MSIsImJvZHkyIiwibGV0dGVyU3BhY2luZyIsInN1YnRpdGxlMSIsInN1YnRpdGxlMiIsImNvbXBvbmVudHMiLCJNdWlDc3NCYXNlbGluZSIsInN0eWxlT3ZlcnJpZGVzIiwiYm94U2hhZG93IiwiTXVpQ2FyZCIsInJvb3QiLCJib3JkZXJSYWRpdXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/theme/DefaultColors.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/authentication/login/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/authentication/login/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\authentication\login\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\authentication\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\codes\Improved-CRM\package\src\app\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Loading = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Loading\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loading);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxNQUFNQSxVQUFVO0lBQ1oscUJBQ0ksOERBQUNDO2tCQUFJOzs7Ozs7QUFFYjtBQUVBLGlFQUFlRCxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBMb2FkaW5nID0gKCkgPT57XHJcbiAgICByZXR1cm4oXHJcbiAgICAgICAgPGRpdj5Mb2FkaW5nPC9kaXY+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IExvYWRpbmc7Il0sIm5hbWVzIjpbIkxvYWRpbmciLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLy4vc3JjL2FwcC9mYXZpY29uLmljbz85YzhiIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/react-transition-group","vendor-chunks/stylis","vendor-chunks/react-helmet-async","vendor-chunks/hoist-non-react-statics","vendor-chunks/react-is","vendor-chunks/react-fast-compare","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/invariant","vendor-chunks/shallowequal","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauthentication%2Flogin%2Fpage&page=%2Fauthentication%2Flogin%2Fpage&appPaths=%2Fauthentication%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauthentication%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();