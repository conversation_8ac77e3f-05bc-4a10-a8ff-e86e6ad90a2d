import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuditService } from '@/lib/audit';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const message = await prisma.message.findFirst({
      where: {
        id: params.id,
        OR: [
          { senderId: userId },
          { recipientId: userId },
          { recipientRole: userRole, recipientId: null }
        ]
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        recipient: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        parent: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          }
        },
        replies: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    });

    if (!message) {
      return NextResponse.json(
        { error: 'Message not found' }, 
        { status: 404 }
      );
    }

    // Mark as read if user is the recipient
    if (message.recipientId === userId || 
        (message.recipientRole === userRole && !message.recipientId)) {
      if (!message.isRead) {
        await prisma.message.update({
          where: { id: params.id },
          data: { isRead: true }
        });
        message.isRead = true;
      }
    }

    return NextResponse.json(message);
  } catch (error) {
    console.error('Failed to fetch message:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message' }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { isRead } = body;

    // Find the message first to ensure user has access
    const existingMessage = await prisma.message.findFirst({
      where: {
        id: params.id,
        OR: [
          { recipientId: userId },
          { recipientRole: userRole, recipientId: null }
        ]
      }
    });

    if (!existingMessage) {
      return NextResponse.json(
        { error: 'Message not found or access denied' }, 
        { status: 404 }
      );
    }

    const updatedMessage = await prisma.message.update({
      where: { id: params.id },
      data: {
        isRead: isRead !== undefined ? isRead : true,
        updatedAt: new Date()
      }
    });

    // Log the action
    await AuditService.log(
      userId,
      'UPDATE',
      'Message',
      params.id,
      existingMessage,
      updatedMessage,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(updatedMessage);
  } catch (error) {
    console.error('Failed to update message:', error);
    return NextResponse.json(
      { error: 'Failed to update message' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Find the message first to ensure user is the sender
    const existingMessage = await prisma.message.findFirst({
      where: {
        id: params.id,
        senderId: userId // Only sender can delete messages
      }
    });

    if (!existingMessage) {
      return NextResponse.json(
        { error: 'Message not found or access denied' }, 
        { status: 404 }
      );
    }

    // Delete the message and its replies
    await prisma.message.deleteMany({
      where: {
        OR: [
          { id: params.id },
          { parentId: params.id }
        ]
      }
    });

    // Log the action
    await AuditService.log(
      userId,
      'DELETE',
      'Message',
      params.id,
      existingMessage,
      null,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete message:', error);
    return NextResponse.json(
      { error: 'Failed to delete message' }, 
      { status: 500 }
    );
  }
}
