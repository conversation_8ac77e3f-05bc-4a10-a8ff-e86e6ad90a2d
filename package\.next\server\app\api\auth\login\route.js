"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "@prisma/client?9bbd":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_Improved_CRM_package_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_Improved_CRM_package_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate input\n        const validationResult = _lib_validation__WEBPACK_IMPORTED_MODULE_2__.loginSchema.safeParse(body);\n        if (!validationResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: validationResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const { email, password } = validationResult.data;\n        // Get client info\n        const ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"unknown\";\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // Attempt login\n        const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.login(email, password, ipAddress, userAgent);\n        if (!result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error\n            }, {\n                status: 401\n            });\n        }\n        // Create response with tokens\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: result.user,\n            accessToken: result.accessToken\n        });\n        // Set secure HTTP-only cookies\n        response.cookies.set(\"accessToken\", result.accessToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 15 * 60,\n            path: \"/\"\n        });\n        response.cookies.set(\"refreshToken\", result.refreshToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 7 * 24 * 60 * 60,\n            path: \"/\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?9bbd\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-in-production\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key-change-in-production\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"15m\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nclass AuthService {\n    // Hash password\n    static async hashPassword(password) {\n        const saltRounds = 12;\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n    }\n    // Verify password\n    static async verifyPassword(password, hashedPassword) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n    }\n    // Generate JWT tokens\n    static generateTokens(payload) {\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Login user\n    static async login(email, password, ipAddress, userAgent) {\n        try {\n            // Find user by email\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                where: {\n                    email: email.toLowerCase()\n                }\n            });\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Check if user is active\n            if (!user.isActive) {\n                return {\n                    success: false,\n                    error: \"Account is deactivated\"\n                };\n            }\n            // Check if user is locked\n            if (user.isLocked) {\n                return {\n                    success: false,\n                    error: \"Account is locked due to too many failed attempts\"\n                };\n            }\n            // Verify password\n            const isPasswordValid = await this.verifyPassword(password, user.password);\n            if (!isPasswordValid) {\n                // Increment failed attempts\n                await this.handleFailedLogin(user.id);\n                return {\n                    success: false,\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Reset failed attempts on successful login\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    failedAttempts: 0,\n                    lastFailedAttempt: null,\n                    lastLoginAt: new Date()\n                }\n            });\n            // Generate tokens\n            const payload = {\n                userId: user.id,\n                email: user.email,\n                role: user.role,\n                name: user.name\n            };\n            const { accessToken, refreshToken } = this.generateTokens(payload);\n            // Store refresh token in database\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    refreshToken\n                }\n            });\n            // Log successful login\n            await this.logAuditEvent(user.id, \"LOGIN\", \"User\", user.id, null, null, ipAddress, userAgent);\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                },\n                accessToken,\n                refreshToken\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during login\"\n            };\n        }\n    }\n    // Handle failed login attempts\n    static async handleFailedLogin(userId) {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        if (!user) return;\n        const newFailedAttempts = user.failedAttempts + 1;\n        const shouldLock = newFailedAttempts >= 5;\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                failedAttempts: newFailedAttempts,\n                lastFailedAttempt: new Date(),\n                isLocked: shouldLock\n            }\n        });\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            const payload = this.verifyToken(refreshToken, true);\n            if (!payload) {\n                return {\n                    success: false,\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Verify refresh token exists in database\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                where: {\n                    id: payload.userId,\n                    refreshToken,\n                    isActive: true\n                }\n            });\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Generate new tokens\n            const newPayload = {\n                userId: user.id,\n                email: user.email,\n                role: user.role,\n                name: user.name\n            };\n            const tokens = this.generateTokens(newPayload);\n            // Update refresh token in database\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    refreshToken: tokens.refreshToken\n                }\n            });\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                },\n                accessToken: tokens.accessToken,\n                refreshToken: tokens.refreshToken\n            };\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during token refresh\"\n            };\n        }\n    }\n    // Logout user\n    static async logout(userId, ipAddress, userAgent) {\n        try {\n            // Clear refresh token\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                where: {\n                    id: userId\n                },\n                data: {\n                    refreshToken: null\n                }\n            });\n            // Log logout\n            await this.logAuditEvent(userId, \"LOGOUT\", \"User\", userId, null, null, ipAddress, userAgent);\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    }\n    // Register new user\n    static async register(email, password, name, role = _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.STUDENT, ipAddress, userAgent) {\n        try {\n            // Check if user already exists\n            const existingUser = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                where: {\n                    email: email.toLowerCase()\n                }\n            });\n            if (existingUser) {\n                return {\n                    success: false,\n                    error: \"User with this email already exists\"\n                };\n            }\n            // Hash password\n            const hashedPassword = await this.hashPassword(password);\n            // Create user\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.create({\n                data: {\n                    email: email.toLowerCase(),\n                    password: hashedPassword,\n                    name,\n                    role\n                }\n            });\n            // Log user creation\n            await this.logAuditEvent(user.id, \"CREATE\", \"User\", user.id, null, {\n                email,\n                name,\n                role\n            }, ipAddress, userAgent);\n            return {\n                success: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    isActive: user.isActive\n                }\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"An error occurred during registration\"\n            };\n        }\n    }\n    // Log audit events\n    static async logAuditEvent(userId, action, entity, entityId, oldData, newData, ipAddress, userAgent) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n                data: {\n                    userId,\n                    action,\n                    entity,\n                    entityId,\n                    oldData: oldData ? JSON.stringify(oldData) : null,\n                    newData: newData ? JSON.stringify(newData) : null,\n                    ipAddress,\n                    userAgent\n                }\n            });\n        } catch (error) {\n            console.error(\"Audit log error:\", error);\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        return _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                id: true,\n                email: true,\n                name: true,\n                role: true,\n                isActive: true,\n                lastLoginAt: true,\n                createdAt: true\n            }\n        });\n    }\n    // Check permissions\n    static hasPermission(userRole, requiredRole) {\n        const roleHierarchy = {\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.STUDENT]: 0,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.TEACHER]: 1,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.RECEPTION]: 2,\n            [_prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.ADMIN]: 3\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client?9bbd\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUVqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm5pemUtbmV4dC1mcmVlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfVxyXG5cclxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWwiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attendanceSchema: () => (/* binding */ attendanceSchema),\n/* harmony export */   cabinetSchema: () => (/* binding */ cabinetSchema),\n/* harmony export */   changePasswordSchema: () => (/* binding */ changePasswordSchema),\n/* harmony export */   classSchema: () => (/* binding */ classSchema),\n/* harmony export */   gradeSchema: () => (/* binding */ gradeSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   paymentSchema: () => (/* binding */ paymentSchema),\n/* harmony export */   registerSchema: () => (/* binding */ registerSchema),\n/* harmony export */   studentSchema: () => (/* binding */ studentSchema),\n/* harmony export */   teacherSchema: () => (/* binding */ teacherSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Authentication schemas\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, \"Password must be at least 6 characters\")\n});\nconst registerSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, \"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\"),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"ADMIN\",\n        \"RECEPTION\",\n        \"TEACHER\",\n        \"STUDENT\"\n    ]).default(\"STUDENT\")\n});\nconst changePasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    currentPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Current password is required\"),\n    newPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, \"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n}).refine((data)=>data.newPassword === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Student schemas\nconst studentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10, \"Phone number must be at least 10 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\").optional(),\n    joinDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    paymentStatus: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PAID\",\n        \"UNPAID\",\n        \"PARTIAL\"\n    ]).default(\"UNPAID\"),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"INQUIRY\",\n        \"LEAD\",\n        \"ENROLLED\",\n        \"GRADUATED\",\n        \"DROPPED\",\n        \"SUSPENDED\"\n    ]).default(\"INQUIRY\"),\n    parentName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    parentPhone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    emergencyContact: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    classIds: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional()\n});\n// Teacher schemas\nconst teacherSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Name must be at least 2 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Invalid email address\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10, \"Phone number must be at least 10 characters\"),\n    subjects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).min(1, \"At least one subject is required\"),\n    qualifications: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).min(1, \"At least one qualification is required\"),\n    joinDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"ACTIVE\",\n        \"INACTIVE\",\n        \"ON_LEAVE\"\n    ]).default(\"ACTIVE\"),\n    salary: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    emergencyContact: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Class schemas\nconst classSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Class name must be at least 2 characters\"),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Subject must be at least 2 characters\"),\n    level: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Level is required\"),\n    stage: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"EARLY\",\n        \"MIDDLE\",\n        \"LATE\"\n    ]).default(\"EARLY\"),\n    language: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"RUSSIAN\",\n        \"UZBEK\",\n        \"MIXED\"\n    ]).default(\"RUSSIAN\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    cabinetId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Cabinet is required\"),\n    courseAmount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Course amount must be positive\"),\n    maxStudents: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive().default(15),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    openingDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    schedule: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        day: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        startTime: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        endTime: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })),\n    studentIds: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional()\n});\n// Payment schemas\nconst paymentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Amount must be positive\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PENDING\",\n        \"COMPLETED\",\n        \"FAILED\",\n        \"REFUNDED\"\n    ]).default(\"PENDING\"),\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"CASH\",\n        \"CARD\",\n        \"BANK_TRANSFER\",\n        \"ONLINE\"\n    ]),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Description is required\"),\n    invoiceNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional()\n});\n// Cabinet schemas\nconst cabinetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Cabinet name is required\"),\n    capacity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(\"Capacity must be positive\"),\n    equipment: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).default([]),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"AVAILABLE\",\n        \"OCCUPIED\",\n        \"MAINTENANCE\"\n    ]).default(\"AVAILABLE\"),\n    location: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Location is required\")\n});\n// Attendance schemas\nconst attendanceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    classId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Class is required\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"PRESENT\",\n        \"ABSENT\",\n        \"LATE\",\n        \"EXCUSED\"\n    ]),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Grade schemas\nconst gradeSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    studentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Student is required\"),\n    teacherId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Teacher is required\"),\n    classId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Class is required\"),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Subject is required\"),\n    gradeType: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"QUIZ\",\n        \"EXAM\",\n        \"HOMEWORK\",\n        \"PROJECT\"\n    ]),\n    score: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, \"Score cannot be negative\"),\n    maxScore: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Max score must be positive\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n}).refine((data)=>data.score <= data.maxScore, {\n    message: \"Score cannot be greater than max score\",\n    path: [\n        \"score\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CImproved-CRM%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();