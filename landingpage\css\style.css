/*
   Innovative Centre - Main Stylesheet
   Author: Augment Agent
   Version: 1.0
*/

/* ===== RESET & BASE STYLES ===== */
:root {
    /* Primary Brand Colors */
    --primary-color: #0056b3;
    --primary-dark: #004494;
    --primary-light: #3378c5;

    /* Secondary Colors */
    --secondary-color: #ff6b00;
    --secondary-dark: #e05e00;
    --secondary-light: #ff8c3f;

    /* Accent Colors */
    --accent-color: #00a0e3;
    --accent-dark: #0088c3;
    --accent-light: #33b5e9;

    /* Neutral Colors */
    --text-color: #333333;
    --text-light: #666666;
    --text-lighter: #999999;
    --text-white: #ffffff;

    /* Background Colors */
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-gray: #f0f2f5;
    --bg-dark: #1a1a1a;

    /* UI Elements */
    --border-color: #e0e0e0;
    --border-light: #f0f0f0;
    --border-dark: #cccccc;

    /* Effects */
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.08);
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

section {
    padding: 80px 0;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.section-subtitle {
    font-size: 18px;
    text-align: center;
    margin-bottom: 50px;
    color: var(--text-light);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: none;
    outline: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width 0.3s ease;
    z-index: -1;
}

.btn:hover::before {
    width: 100%;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(0, 86, 179, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-white);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
    transform: translateY(-2px);
}

.btn-video {
    background-color: transparent;
    color: var(--text-white);
    border: 2px solid var(--text-white);
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-video:hover {
    background-color: var(--text-white);
    color: var(--primary-color);
}

/* ===== HEADER STYLES ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: var(--transition);
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    height: 80px;
    width: 240px; /* Fixed width based on aspect ratio */
    display: flex;
    align-items: center;
}

.logo img {
    height: 100%;
    width: 100%;
    transition: all 0.3s ease;
    object-fit: contain;
    max-width: 100%;
}

.header.scrolled .logo {
    height: 65px;
    width: 195px; /* Maintain the same aspect ratio when scrolled */
}

.main-nav {
    display: flex;
}

.nav-list {
    display: flex;
    gap: 25px;
}

.nav-link {
    color: var(--text-white);
    font-weight: 500;
    position: relative;
    padding: 5px 0;
}

.header.scrolled .nav-link {
    color: var(--text-color);
}

.nav-link.active::after,
.nav-link:hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--secondary-color);
    transform: scaleX(1);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--secondary-color);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
    box-shadow: 0 0 8px rgba(255, 107, 0, 0.5);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    min-width: 200px;
    box-shadow: var(--shadow);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
    padding: 10px 0;
    z-index: 1;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 8px 20px;
    color: var(--text-color);
}

.dropdown-menu a:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-phone, .header-social {
    color: var(--text-white);
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    transition: var(--transition);
}

.header.scrolled .header-phone,
.header.scrolled .header-social {
    color: var(--text-color);
}

.header-phone:hover,
.header-social:hover {
    color: var(--secondary-color);
}

.header-social i {
    font-size: 18px;
}

.language-selector {
    position: relative;
    cursor: pointer;
}

.current-lang {
    color: var(--text-white);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.header.scrolled .current-lang {
    color: var(--text-color);
}

.current-lang::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    min-width: 100px;
    box-shadow: var(--shadow);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
    padding: 10px 0;
    z-index: 1;
}

.language-selector:hover .lang-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-dropdown a {
    display: block;
    padding: 5px 15px;
    color: var(--text-color);
}

.lang-dropdown a:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.contact-btn {
    background-color: var(--secondary-color);
    color: var(--text-white);
    padding: 8px 20px;
    border-radius: 50px;
    font-weight: 500;
}

.contact-btn:hover {
    background-color: #e05e00;
    color: var(--text-white);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-white);
    transition: var(--transition);
}

.header.scrolled .mobile-menu-toggle span {
    background-color: var(--text-color);
}

/* ===== CHECKBOX STYLES ===== */
.course-checkboxes {
    margin-top: 15px;
}

.checkbox-label {
    font-weight: 500;
    margin-bottom: 10px;
    color: #000;
}

.checkbox-row {
    margin-bottom: 10px;
    display: flex;
}

.checkbox-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-item label {
    cursor: pointer;
    font-size: 15px;
    color: #000;
}

/* ===== HERO SECTION ===== */
.hero-section {
    height: 100vh;
    min-height: 700px;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/6.jpg') no-repeat center center/cover;
    display: flex;
    align-items: center;
    color: var(--text-white);
    padding-top: 80px;
}

.hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.hero-content {
    flex: 1;
    text-align: left;
    max-width: 600px;
}

.hero-form-container {
    flex: 1;
    max-width: 450px;
}

.hero-form {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-form h3 {
    color: var(--primary-color);
    font-size: 24px;
    margin-bottom: 5px;
    text-align: center;
}

.form-subtitle {
    color: var(--text-light);
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
    outline: none;
    transform: translateY(-2px);
    transition: var(--transition);
}

.btn-block {
    width: 100%;
    margin-top: 10px;
}

.form-note {
    text-align: center;
    color: var(--secondary-color);
    font-size: 14px;
    margin-top: 15px;
    font-weight: 500;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
}

.hero-feature i {
    color: var(--secondary-color);
}

.form-success {
    text-align: center;
    padding: 20px 0;
}

.form-success i {
    font-size: 50px;
    color: #4CAF50;
    margin-bottom: 15px;
}

.form-success h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.form-success p {
    color: var(--text-light);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    animation: fadeInUp 1s ease-out;
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
    animation: fadeInUp 1s ease-out 0.3s;
    animation-fill-mode: both;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    animation: fadeInUp 1s ease-out 0.6s;
    animation-fill-mode: both;
}

/* ===== STATS SECTION ===== */
.stats-section {
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: 60px 0;
}

.stats-header {
    text-align: center;
    margin-bottom: 40px;
}

.stats-header h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
}

.stats-wrapper {
    display: flex;
    justify-content: space-around;
    text-align: center;
    flex-wrap: wrap;
    gap: 30px;
}

.stat-item {
    flex: 1;
    min-width: 200px;
    padding: 20px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-10px);
    background-color: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    font-size: 30px;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.stat-item h3 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-item p {
    font-size: 16px;
    opacity: 0.9;
}

.stats-cta {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.stats-cta p {
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 500;
}

/* IELTS Results Styles */
.success-stories-container {
    margin-top: 50px;
    margin-bottom: 50px;
}

.text-center {
    text-align: center;
}

.section-subtitle {
    font-size: 24px;
    margin-bottom: 30px;
    color: #fff;
    font-weight: 600;
}

/* IELTS Results Grid */
.ielts-results-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

/* IELTS Result Cards */
.ielts-result-card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ielts-result-card:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.result-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    max-height: 300px;
    transition: transform 0.5s ease;
}

.ielts-result-card:hover .result-image img {
    transform: scale(1.05);
}

.result-info {
    padding: 20px;
    text-align: center;
}

.result-score {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.result-score .score {
    font-size: 36px;
    font-weight: 700;
    color: #ff9900;
    text-shadow: 0 0 10px rgba(255, 153, 0, 0.3);
}

.result-score .label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

.result-name {
    font-size: 18px;
    color: #fff;
    font-weight: 500;
}

/* Motivation Card */
.ielts-motivation-card {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    height: 100%;
    text-align: center;
    border: 2px dashed rgba(255, 255, 255, 0.3);
}

.motivation-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.motivation-content h3 {
    font-size: 28px;
    color: #fff;
    margin-bottom: 10px;
}

/* Mirror Effect */
.mirror-effect {
    width: 200px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    margin: 0 auto 20px;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mirror-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    z-index: 1;
}

.mirror-effect::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: mirror-shine 5s infinite linear;
    z-index: 2;
    pointer-events: none;
}

@keyframes mirror-shine {
    0% {
        transform: rotate(0deg);
        opacity: 0.3;
    }
    25% {
        opacity: 0.5;
    }
    50% {
        opacity: 0.3;
    }
    75% {
        opacity: 0.5;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0.3;
    }
}

.person-silhouette {
    width: 150px;
    height: 150px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    position: relative;
}

/* Results Carousel */
.results-carousel-container {
    margin-top: 40px;
    padding: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.results-carousel-container h4 {
    text-align: center;
    color: #fff;
    font-size: 20px;
    margin-bottom: 20px;
}

.results-carousel {
    width: 100%;
    padding-bottom: 40px;
    padding-left: 30px;
    padding-right: 30px;
}

.carousel-result {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.carousel-result:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.2);
}

.carousel-result img {
    width: 100%;
    height: 220px;
    object-fit: contain;
}

.carousel-result-info {
    padding: 15px;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.carousel-result-info .score {
    font-size: 24px;
    font-weight: 700;
    color: #ff9900;
}

.carousel-result-info .name {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

/* Results Carousel Navigation */
.results-carousel .swiper-button-next,
.results-carousel .swiper-button-prev {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    transition: var(--transition);
}

.results-carousel .swiper-button-next:hover,
.results-carousel .swiper-button-prev:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.results-carousel .swiper-button-next:after,
.results-carousel .swiper-button-prev:after {
    font-size: 16px;
    font-weight: bold;
}

.results-carousel .swiper-pagination-bullet {
    background-color: #fff;
    opacity: 0.5;
}

.results-carousel .swiper-pagination-bullet-active {
    background-color: var(--secondary-color);
    opacity: 1;
}

/* ===== WHY US SECTION ===== */
.why-us-section {
    background-color: var(--bg-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.feature-item {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-10px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.feature-item:nth-child(2) .feature-icon,
.feature-item:nth-child(5) .feature-icon {
    background-color: var(--secondary-color);
}

.feature-item:nth-child(3) .feature-icon,
.feature-item:nth-child(6) .feature-icon {
    background-color: var(--accent-color);
}

.feature-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
}

/* ===== EXAMS SECTION ===== */
.exams-section {
    background-color: #fff;
}

.exam-card {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 50px;
    background-color: var(--bg-light);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.exam-card.reverse {
    flex-direction: row-reverse;
}

.exam-content {
    flex: 1;
    padding: 40px;
}

.exam-logo {
    height: 50px;
    margin-bottom: 20px;
    object-fit: contain;
}

.exam-content h2 {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.exam-content p {
    margin-bottom: 20px;
}

.exam-buttons {
    display: flex;
    gap: 15px;
}

.exam-image {
    flex: 1;
    height: 400px;
}

.exam-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* ===== COURSES SECTION ===== */
.courses-section {
    background-color: var(--bg-light);
    padding: 80px 0 100px;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.course-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.course-badge {
    position: absolute;
    top: 25px;
    right: -50px;
    background-color: var(--secondary-color);
    color: #fff;
    padding: 8px 0;
    font-size: 15px;
    font-weight: 700;
    transform: rotate(45deg);
    z-index: 1;
    width: 200px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(255, 107, 0, 0.3);
}

.course-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 28px;
}

.course-card:nth-child(2) .course-icon {
    background-color: var(--secondary-color);
}

.course-card:nth-child(3) .course-icon {
    background-color: var(--accent-color);
}

.course-card h3 {
    font-size: 22px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.course-features {
    margin-bottom: 25px;
    flex-grow: 1;
}

.course-features li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.course-features i {
    color: var(--secondary-color);
    margin-top: 5px;
}

.course-pricing {
    display: flex;
    align-items: baseline;
    gap: 10px;
    margin-bottom: 20px;
}

.price-old {
    color: var(--text-light);
    text-decoration: line-through;
    font-size: 16px;
}

.price-current {
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: 700;
}

.price-period {
    color: var(--text-light);
    font-size: 14px;
}

.course-card .btn {
    width: 100%;
}

.courses-cta {
    background: var(--gradient-primary);
    padding: 40px;
    border-radius: 10px;
    color: var(--text-white);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.countdown-container {
    margin-bottom: 25px;
}

.countdown-title {
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
}

.countdown {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.countdown-number {
    font-size: 36px;
    font-weight: 700;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.countdown-label {
    font-size: 14px;
    font-weight: 500;
}

.courses-cta-text {
    font-size: 18px;
    margin-bottom: 25px;
    font-weight: 500;
}

.btn-lg {
    padding: 15px 40px;
    font-size: 18px;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    background-color: #fff;
    position: relative;
    overflow: hidden;
    padding: 80px 0 100px;
}

/* Video Testimonials Grid */
.video-testimonials-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    max-width: 1100px;
    margin: 0 auto;
}

.main-video {
    grid-row: span 2;
    height: 100%;
}

.video-grid-right {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
}

.video-testimonial-item {
    background-color: var(--bg-light);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
}

.video-testimonial-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.video-wrapper {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.main-video .video-wrapper {
    height: 400px;
}

.video-grid-right .video-wrapper {
    height: 180px;
}

.video-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.video-testimonial-item:hover .video-wrapper img {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.video-overlay i {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary-color);
    transition: var(--transition);
}

.main-video .video-overlay i {
    width: 80px;
    height: 80px;
    font-size: 30px;
}

.video-testimonial-item:hover .video-overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.video-testimonial-item:hover .video-overlay i {
    background-color: var(--primary-color);
    color: #fff;
    transform: scale(1.1);
}

.video-info {
    padding: 15px;
}

.main-video .video-info {
    padding: 20px;
}

.video-info h3 {
    font-size: 16px;
    margin-bottom: 5px;
    color: var(--text-color);
}

.main-video .video-info h3 {
    font-size: 20px;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.video-score {
    display: flex;
    align-items: baseline;
    gap: 5px;
}

.video-score .score {
    font-size: 18px;
    font-weight: 700;
    color: var(--secondary-color);
}

.main-video .video-score .score {
    font-size: 24px;
}

.video-score .label {
    font-size: 12px;
    color: var(--text-color);
}

.video-level {
    font-size: 12px;
    color: var(--text-color);
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .video-testimonials-grid {
        grid-template-columns: 1fr;
    }

    .main-video {
        grid-row: span 1;
    }

    .video-grid-right {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .video-grid-right {
        grid-template-columns: 1fr;
    }
}



/* ===== TEAM SECTION ===== */
.team-section {
    background-color: #fff;
}

.team-section h3.section-subtitle {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 40px 0 20px;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.team-section h3.section-subtitle::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

/* Team Categories */
.team-categories {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 20px;
    border-radius: 50px;
    background-color: var(--bg-light);
    border: none;
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.category-btn:hover {
    background-color: rgba(0, 86, 179, 0.1);
    color: var(--primary-color);
}

.category-btn.active {
    background-color: var(--primary-color);
    color: #fff;
}

/* Team Carousel */
.team-carousel {
    width: 100%;
    padding-bottom: 60px;
    margin-bottom: 30px;
}

.team-member {
    background-color: var(--bg-light);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-social {
    position: absolute;
    bottom: -50px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 15px 0;
    background-color: rgba(0, 0, 0, 0.7);
    transition: var(--transition);
}

.team-member:hover .member-social {
    bottom: 0;
}

.member-social a {
    width: 35px;
    height: 35px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.member-social a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-5px);
}

.member-info {
    padding: 25px;
}

.member-info h3 {
    font-size: 20px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.member-position {
    font-size: 16px;
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.member-qualification {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 15px;
}

.member-description {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-color);
}

/* Swiper Navigation */
.team-carousel .swiper-button-next,
.team-carousel .swiper-button-prev {
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.team-carousel .swiper-button-next:after,
.team-carousel .swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
}

.team-carousel .swiper-button-next:hover,
.team-carousel .swiper-button-prev:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.team-carousel .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background-color: var(--primary-color);
    opacity: 0.5;
}

.team-carousel .swiper-pagination-bullet-active {
    opacity: 1;
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.team-cta {
    text-align: center;
}

/* ===== FAQ SECTION ===== */
.faq-section {
    background-color: var(--bg-light);
    padding: 80px 0 100px;
}

.faq-accordion {
    max-width: 800px;
    margin: 0 auto 50px;
}

.faq-cta {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--shadow);
    border-top: 3px solid var(--primary-color);
}

.faq-cta h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.faq-cta p {
    margin-bottom: 20px;
    color: var(--text-light);
}

.faq-item {
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.faq-question {
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question h3 {
    font-size: 18px;
    font-weight: 500;
    transition: var(--transition);
}

.faq-icon {
    color: var(--primary-color);
    transition: var(--transition);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

.faq-item.active {
    border-left: 3px solid var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.faq-item.active .faq-question {
    background-color: rgba(0, 86, 179, 0.05);
}

.faq-item.active .faq-question h3 {
    color: var(--primary-color);
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
    background-color: var(--primary-color);
    color: #fff;
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
    opacity: 0;
}

.faq-item.active .faq-answer {
    padding: 0 20px 20px;
    max-height: 1000px;
    opacity: 1;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    background-color: #fff;
    padding: 80px 0 100px;
}

.contact-wrapper {
    display: flex;
    gap: 40px;
}

.contact-info {
    flex: 1;
}

.contact-details {
    margin: 30px 0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 25px;
}

.contact-item i {
    font-size: 24px;
    color: var(--primary-color);
    margin-top: 5px;
}

.contact-item h3 {
    font-size: 18px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.contact-item p {
    color: var(--text-light);
}

.social-links-container {
    margin-top: 30px;
}

.social-links-container h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--secondary-color);
    color: var(--text-white);
    transform: translateY(-5px);
}

.contact-form-container {
    flex: 1;
}

.contact-form {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-form h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--primary-color);
    text-align: center;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: #fff;
}

/* ===== CTA SECTION ===== */
.cta-section {
    background-color: #fff;
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.05);
    z-index: 0;
}

.cta-section::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(255, 107, 0, 0.05);
    z-index: 0;
}

.cta-wrapper {
    display: flex;
    align-items: center;
    gap: 50px;
    background: linear-gradient(135deg, var(--primary-color), #004494);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.cta-badge {
    display: inline-block;
    background-color: var(--secondary-color);
    color: #fff;
    padding: 5px 15px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(255, 107, 0, 0.3);
}

.cta-content {
    flex: 1;
    padding: 50px;
    color: #fff;
}

.cta-content h2 {
    font-size: 32px;
    margin-bottom: 20px;
    font-weight: 600;
}

.cta-content p {
    margin-bottom: 25px;
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.9;
}

.cta-features {
    margin-bottom: 25px;
    list-style: none;
}

.cta-features li {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.cta-features li i {
    color: var(--secondary-color);
    font-size: 18px;
}

.cta-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.cta-note {
    font-size: 14px;
    opacity: 0.8;
}

.cta-image {
    flex: 1;
    height: 400px;
    position: relative;
}

.cta-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cta-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 86, 179, 0.7), transparent);
    z-index: 1;
}

.cta-testimonial {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    width: 80%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.cta-testimonial-text {
    font-style: italic;
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 14px;
}

.cta-testimonial-author {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cta-testimonial-author img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.cta-testimonial-author span {
    font-weight: 600;
    font-size: 14px;
    color: var(--primary-color);
}

/* ===== PARTNERS SECTION ===== */
.partners-section {
    background-color: var(--bg-light);
    padding: 60px 0;
}

.partners-carousel {
    padding: 20px 0 60px;
}

.partners-carousel .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
}

.partner-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    filter: grayscale(0%);
    opacity: 1;
    transition: var(--transition);
    height: 120px;
}

.partner-logo:hover {
    transform: translateY(-5px);
}

.partner-logo img {
    max-height: 80px;
    max-width: 200px;
    object-fit: contain;
    background-color: transparent;
    border-radius: 5px;
    padding: 5px;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--bg-dark);
    color: var(--text-white);
    padding-top: 80px;
    position: relative;
}

.footer-cta {
    background: linear-gradient(135deg, var(--secondary-color), #e05e00);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 50px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-50%);
    margin-top: -30px;
}

.footer-cta-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #fff;
}

.footer-cta-content p {
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.9);
}

.footer-top {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 50px;
}

.footer-logo img {
    height: 80px;
    margin-bottom: 20px;
    object-fit: contain;
}

.footer-tagline {
    color: #ccc;
    font-size: 14px;
    margin-bottom: 20px;
    max-width: 250px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    color: #ccc;
    font-size: 14px;
}

.footer-bottom-links a:hover {
    color: var(--text-white);
}

.footer-nav {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.footer-nav-column h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--secondary-color);
}

.footer-nav-column ul li {
    margin-bottom: 10px;
}

.footer-nav-column ul li a {
    color: #ccc;
}

.footer-nav-column ul li a:hover {
    color: var(--text-white);
}

.contact-info li {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
}

.contact-info i {
    color: var(--secondary-color);
    margin-top: 5px;
}

.footer .social-links {
    justify-content: flex-start;
    margin-top: 20px;
}

.footer .social-link {
    width: 40px;
    height: 40px;
    font-size: 16px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    text-align: center;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--secondary-color);
    color: var(--text-white);
}
