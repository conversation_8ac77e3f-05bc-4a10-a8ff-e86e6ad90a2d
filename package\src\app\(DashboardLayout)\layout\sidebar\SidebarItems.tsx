import React from "react";
import Menuitems from "./MenuItems";
import { usePathname } from "next/navigation";
import { Box, List } from "@mui/material";
import NavItem from "./NavItem";
import NavGroup from "./NavGroup/NavGroup";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@prisma/client";

const SidebarItems = ({ toggleMobileSidebar }: any) => {
  const pathname = usePathname();
  const pathDirect = pathname;
  const { user, hasPermission } = useAuth();

  const filteredMenuItems = Menuitems.filter(item => {
    // Filter menu items based on user role
    if (item.title === "Главная") { // Dashboard
      return hasPermission(UserRole.RECEPTION); // Admin and Reception can access dashboard
    }
    if (item.title === "Учителя") { // Teachers
      return hasPermission(UserRole.ADMIN); // Only Admin can access teachers
    }
    if (item.title === "Оплата") { // Payments
      return hasPermission(UserRole.ADMIN); // Only Admin can access payments
    }
    if (item.title === "Студенты") { // Students
      return hasPermission(UserRole.RECEPTION); // Admin and Reception can access students
    }
    // All other items are accessible to authenticated users
    return true;
  });

  return (
    <Box sx={{ px: 3 }}>
      <List sx={{ pt: 0 }} className="sidebarNav" component="div">
        {filteredMenuItems.map((item) => {
          // {/********SubHeader**********/}
          if (item.subheader) {
            return <NavGroup item={item} key={item.subheader} />;

            // {/********If Sub Menu**********/}
            /* eslint no-else-return: "off" */
          } else {
            return (
              <NavItem
                item={item}
                key={item.id}
                pathDirect={pathDirect}
                onClick={toggleMobileSidebar}
              />
            );
          }
        })}
      </List>
    </Box>
  );
};
export default SidebarItems;
