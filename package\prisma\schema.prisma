generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Stage {
  EARLY
  MIDDLE
  LATE
}

enum Language {
  RUSSIAN
  UZBEK
  MIXED
}

enum UserRole {
  ADMIN
  RECEPTION
  TEACHER
  STUDENT
}

enum StudentStatus {
  INQUIRY
  LEAD
  ENROLLED
  GRADUATED
  DROPPED
  SUSPENDED
}

model User {
  id                String    @id @default(cuid())
  email            String    @unique
  password         String    // Hashed with bcrypt
  role             UserRole  @default(STUDENT)
  name             String
  isActive         Boolean   @default(true)
  lastLoginAt      DateTime?
  failedAttempts   Int       @default(0)
  lastFailedAttempt DateTime?
  isLocked         Boolean   @default(false)
  twoFactorEnabled Boolean   @default(false)
  twoFactorSecret  String?
  refreshToken     String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  auditLogs        AuditLog[]
  teacherProfile   Teacher?
  studentProfile   Student?

  // Phase 3: Communication & Automation Relations
  notifications    Notification[]
  sentMessages     Message[]      @relation("SentMessages")
  receivedMessages Message[]      @relation("ReceivedMessages")
  announcements    Announcement[]
  automationRules  AutomationRule[]
  emailTemplates   EmailTemplate[]
  emailLogs        EmailLog[]
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  action    String   // CREATE, UPDATE, DELETE, LOGIN, LOGOUT
  entity    String   // User, Student, Teacher, Class, etc.
  entityId  String
  oldData   Json?
  newData   Json?
  ipAddress String
  userAgent String
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([entity, entityId])
  @@index([createdAt])
}

model Teacher {
  id             String   @id @default(cuid())
  userId         String?  @unique
  user           User?    @relation(fields: [userId], references: [id])
  name           String
  email          String   @unique
  phone          String
  subjects       String[]
  qualifications String[]
  joinDate       DateTime
  status         String   @default("ACTIVE")
  salary         Float?
  address        String?
  dateOfBirth    DateTime?
  photoUrl       String?
  emergencyContact String?
  classes        Class[]
  attendances    Attendance[]
  grades         Grade[]
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model Student {
  id               String        @id @default(cuid())
  userId           String?       @unique
  user             User?         @relation(fields: [userId], references: [id])
  name             String
  phone            String
  joinDate         DateTime
  paymentStatus    String        @default("UNPAID")
  status           StudentStatus @default(INQUIRY)
  parentName       String?
  parentPhone      String?
  emergencyContact String?
  photoUrl         String?
  address          String?
  dateOfBirth      DateTime?
  classes          Class[]
  payments         Payment[]
  attendances      Attendance[]
  grades           Grade[]
  documents        Document[]
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
}

model Class {
  id           String    @id @default(cuid())
  name         String
  teacher      Teacher   @relation(fields: [teacherId], references: [id])
  teacherId    String
  subject      String
  level        String
  stage        Stage     @default(EARLY)
  language     Language  @default(RUSSIAN)
  cabinet      Cabinet   @relation(fields: [cabinetId], references: [id])
  cabinetId    String
  schedule     Json[]    // Storing schedule as JSON array
  students     Student[]
  courseAmount Float
  maxStudents  Int       @default(15)
  status       String    @default("ACTIVE") // ACTIVE, COMPLETED, CANCELLED
  description  String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  openingDate  DateTime?
  endDate      DateTime?

  // New relations
  attendances  Attendance[]
  grades       Grade[]
}

model Cabinet {
  id        String  @id @default(cuid())
  name      String
  capacity  Int
  equipment String[]
  status    String
  location  String
  classes   Class[]
}

model Payment {
  id          String   @id @default(cuid())
  student     Student  @relation(fields: [studentId], references: [id])
  studentId   String
  amount      Float
  date        DateTime
  status      String   @default("PENDING")
  method      String
  description String
  invoiceNumber String?
  dueDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Attendance {
  id        String   @id @default(cuid())
  student   Student  @relation(fields: [studentId], references: [id])
  studentId String
  teacher   Teacher  @relation(fields: [teacherId], references: [id])
  teacherId String
  class     Class    @relation(fields: [classId], references: [id])
  classId   String
  date      DateTime
  status    String   // PRESENT, ABSENT, LATE, EXCUSED
  notes     String?
  createdAt DateTime @default(now())

  @@unique([studentId, classId, date])
  @@index([date])
  @@index([classId, date])
}

model Grade {
  id          String   @id @default(cuid())
  student     Student  @relation(fields: [studentId], references: [id])
  studentId   String
  teacher     Teacher  @relation(fields: [teacherId], references: [id])
  teacherId   String
  class       Class    @relation(fields: [classId], references: [id])
  classId     String
  subject     String
  gradeType   String   // QUIZ, EXAM, HOMEWORK, PROJECT
  score       Float
  maxScore    Float
  percentage  Float
  date        DateTime
  notes       String?
  createdAt   DateTime @default(now())

  @@index([studentId])
  @@index([classId])
  @@index([date])
}

model Document {
  id          String   @id @default(cuid())
  student     Student  @relation(fields: [studentId], references: [id])
  studentId   String
  name        String
  type        String   // CONTRACT, CERTIFICATE, ID_COPY, etc.
  fileUrl     String
  uploadDate  DateTime @default(now())
  expiryDate  DateTime?
  isVerified  Boolean  @default(false)
  notes       String?
  createdAt   DateTime @default(now())

  @@index([studentId])
  @@index([type])
}

// Phase 3: Communication & Automation Enums

enum NotificationType {
  PAYMENT_DUE
  PAYMENT_RECEIVED
  CLASS_REMINDER
  CLASS_CANCELLED
  ANNOUNCEMENT
  GRADE_POSTED
  ATTENDANCE_ALERT
  SYSTEM_ALERT
  MESSAGE_RECEIVED
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum MessageType {
  DIRECT
  BROADCAST
  ANNOUNCEMENT
  SYSTEM
}

enum MessagePriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum AnnouncementPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum AutomationTrigger {
  PAYMENT_DUE
  PAYMENT_OVERDUE
  CLASS_STARTING
  STUDENT_ABSENT
  GRADE_BELOW_THRESHOLD
  ENROLLMENT_EXPIRING
  DOCUMENT_EXPIRING
  SCHEDULED_TIME
}

enum EmailCategory {
  WELCOME
  PAYMENT_REMINDER
  CLASS_NOTIFICATION
  ANNOUNCEMENT
  GRADE_REPORT
  ATTENDANCE_REPORT
  SYSTEM_NOTIFICATION
}

enum EmailStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  BOUNCED
}

// Phase 3: Communication & Automation Models

model Notification {
  id          String             @id @default(cuid())
  userId      String
  type        NotificationType
  title       String
  message     String
  data        Json?              // Additional data for the notification
  isRead      Boolean            @default(false)
  priority    NotificationPriority @default(NORMAL)
  category    String?            // payment, class, announcement, etc.
  actionUrl   String?            // URL to navigate when clicked
  expiresAt   DateTime?          // Optional expiration date
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@index([userId, isRead])
  @@index([createdAt])
}

model Message {
  id            String        @id @default(cuid())
  senderId      String
  recipientId   String?       // null for broadcast messages
  recipientRole UserRole?     // for role-based messaging
  subject       String
  content       String
  messageType   MessageType   @default(DIRECT)
  priority      MessagePriority @default(NORMAL)
  isRead        Boolean       @default(false)
  parentId      String?       // for reply threading
  attachments   Json?         // file attachments metadata
  scheduledFor  DateTime?     // for scheduled messages
  sentAt        DateTime?     // when actually sent
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  sender        User          @relation("SentMessages", fields: [senderId], references: [id])
  recipient     User?         @relation("ReceivedMessages", fields: [recipientId], references: [id])
  parent        Message?      @relation("MessageReplies", fields: [parentId], references: [id])
  replies       Message[]     @relation("MessageReplies")

  @@index([recipientId, isRead])
  @@index([senderId])
  @@index([createdAt])
}

model Announcement {
  id          String              @id @default(cuid())
  authorId    String
  title       String
  content     String
  targetRole  UserRole?           // null for all users
  priority    AnnouncementPriority @default(NORMAL)
  isActive    Boolean             @default(true)
  isPinned    Boolean             @default(false)
  attachments Json?               // file attachments metadata
  publishAt   DateTime            @default(now())
  expiresAt   DateTime?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Relations
  author User @relation(fields: [authorId], references: [id])

  @@index([isActive, publishAt])
  @@index([targetRole])
}

model AutomationRule {
  id          String            @id @default(cuid())
  name        String
  description String?
  trigger     AutomationTrigger
  conditions  Json              // Conditions to check
  actions     Json              // Actions to perform
  isActive    Boolean           @default(true)
  lastRun     DateTime?
  runCount    Int               @default(0)
  createdBy   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  creator User @relation(fields: [createdBy], references: [id])
  logs    AutomationLog[]

  @@index([isActive])
  @@index([trigger])
}

model AutomationLog {
  id          String   @id @default(cuid())
  ruleId      String
  status      String   // SUCCESS, FAILED, SKIPPED
  message     String?
  data        Json?    // Execution data
  executedAt  DateTime @default(now())

  // Relations
  rule AutomationRule @relation(fields: [ruleId], references: [id])

  @@index([ruleId, executedAt])
}

model EmailTemplate {
  id          String            @id @default(cuid())
  name        String            @unique
  subject     String
  htmlContent String
  textContent String?
  variables   Json?             // Template variables
  category    EmailCategory
  isActive    Boolean           @default(true)
  createdBy   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  creator User @relation(fields: [createdBy], references: [id])
  logs    EmailLog[]

  @@index([category, isActive])
}

model EmailLog {
  id          String      @id @default(cuid())
  templateId  String?
  recipientId String
  subject     String
  status      EmailStatus @default(PENDING)
  sentAt      DateTime?
  failureReason String?
  metadata    Json?       // Additional email metadata
  createdAt   DateTime    @default(now())

  // Relations
  template  EmailTemplate? @relation(fields: [templateId], references: [id])
  recipient User          @relation(fields: [recipientId], references: [id])

  @@index([recipientId, status])
  @@index([sentAt])
}