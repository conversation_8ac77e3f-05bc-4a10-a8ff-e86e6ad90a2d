import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { gradeSchema } from '@/lib/validation';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can view their class grades, Admin/Reception can view all
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');
    const studentId = searchParams.get('studentId');
    const subject = searchParams.get('subject');
    const gradeType = searchParams.get('gradeType');

    // Build filter conditions
    const where: any = {};
    
    if (classId) where.classId = classId;
    if (studentId) where.studentId = studentId;
    if (subject) where.subject = subject;
    if (gradeType) where.gradeType = gradeType;

    // If user is a teacher, only show grades for their classes
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (teacher) {
        where.teacherId = teacher.id;
      }
    }

    const grades = await prisma.grade.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
      orderBy: [
        { date: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    // Calculate percentage for each grade
    const gradesWithPercentage = grades.map(grade => ({
      ...grade,
      percentage: Math.round((grade.score / grade.maxScore) * 100),
    }));

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'READ',
      'Grade',
      'query',
      null,
      { filters: where },
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(gradesWithPercentage);
  } catch (error) {
    console.error('Get grades error:', error);
    return NextResponse.json({ error: 'Failed to fetch grades' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can add grades for their classes, Admin/Reception can add any
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate input
    const validationResult = gradeSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        }, 
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // If user is a teacher, verify they can add grades for this class
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (!teacher || teacher.id !== data.teacherId) {
        return NextResponse.json({ error: 'You can only add grades for your own classes' }, { status: 403 });
      }
    }

    // Calculate percentage
    const percentage = Math.round((data.score / data.maxScore) * 100);

    const grade = await prisma.grade.create({
      data: {
        studentId: data.studentId,
        teacherId: data.teacherId,
        classId: data.classId,
        subject: data.subject,
        gradeType: data.gradeType,
        score: data.score,
        maxScore: data.maxScore,
        percentage,
        date: new Date(data.date),
        notes: data.notes,
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'CREATE',
      'Grade',
      grade.id,
      null,
      grade,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(grade);
  } catch (error) {
    console.error('Create grade error:', error);
    return NextResponse.json({ error: 'Failed to create grade record' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Teachers can update their class grades, Admin/Reception can update any
    if (!AuthService.hasPermission(userRole, UserRole.TEACHER)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json({ error: 'Grade ID is required' }, { status: 400 });
    }

    // Validate input
    const validationResult = gradeSchema.partial().safeParse(updateData);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        }, 
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Get current grade data for audit log and permission check
    const currentGrade = await prisma.grade.findUnique({
      where: { id },
      include: {
        student: true,
        teacher: true,
        class: true,
      },
    });

    if (!currentGrade) {
      return NextResponse.json({ error: 'Grade record not found' }, { status: 404 });
    }

    // If user is a teacher, verify they can update this grade record
    if (userRole === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId },
        select: { id: true },
      });
      
      if (!teacher || teacher.id !== currentGrade.teacherId) {
        return NextResponse.json({ error: 'You can only update grades for your own classes' }, { status: 403 });
      }
    }

    // Calculate new percentage if score or maxScore changed
    let percentage = currentGrade.percentage;
    if (data.score !== undefined || data.maxScore !== undefined) {
      const newScore = data.score ?? currentGrade.score;
      const newMaxScore = data.maxScore ?? currentGrade.maxScore;
      percentage = Math.round((newScore / newMaxScore) * 100);
    }

    const grade = await prisma.grade.update({
      where: { id },
      data: {
        subject: data.subject,
        gradeType: data.gradeType,
        score: data.score,
        maxScore: data.maxScore,
        percentage,
        date: data.date ? new Date(data.date) : undefined,
        notes: data.notes,
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        class: {
          select: {
            id: true,
            name: true,
            subject: true,
            level: true,
          },
        },
      },
    });

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'UPDATE',
      'Grade',
      grade.id,
      currentGrade,
      grade,
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(grade);
  } catch (error) {
    console.error('Update grade error:', error);
    return NextResponse.json({ error: 'Failed to update grade record' }, { status: 500 });
  }
}
