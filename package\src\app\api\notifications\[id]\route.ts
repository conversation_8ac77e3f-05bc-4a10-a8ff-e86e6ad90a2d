import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuditService } from '@/lib/audit';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const notification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: userId // Ensure user can only access their own notifications
      }
    });

    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' }, 
        { status: 404 }
      );
    }

    return NextResponse.json(notification);
  } catch (error) {
    console.error('Failed to fetch notification:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification' }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { isRead } = body;

    // Find the notification first to ensure it belongs to the user
    const existingNotification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: userId
      }
    });

    if (!existingNotification) {
      return NextResponse.json(
        { error: 'Notification not found' }, 
        { status: 404 }
      );
    }

    const updatedNotification = await prisma.notification.update({
      where: { id: params.id },
      data: {
        isRead: isRead !== undefined ? isRead : true,
        updatedAt: new Date()
      }
    });

    // Log the action
    await AuditService.log(
      userId,
      'UPDATE',
      'Notification',
      params.id,
      existingNotification,
      updatedNotification,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(updatedNotification);
  } catch (error) {
    console.error('Failed to update notification:', error);
    return NextResponse.json(
      { error: 'Failed to update notification' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    // Find the notification first to ensure it belongs to the user
    const existingNotification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: userId
      }
    });

    if (!existingNotification) {
      return NextResponse.json(
        { error: 'Notification not found' }, 
        { status: 404 }
      );
    }

    await prisma.notification.delete({
      where: { id: params.id }
    });

    // Log the action
    await AuditService.log(
      userId,
      'DELETE',
      'Notification',
      params.id,
      existingNotification,
      null,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete notification:', error);
    return NextResponse.json(
      { error: 'Failed to delete notification' }, 
      { status: 500 }
    );
  }
}
