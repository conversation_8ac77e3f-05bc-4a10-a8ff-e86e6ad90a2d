import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Only Admin can view audit logs
    if (!AuthService.hasPermission(userRole, UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const action = searchParams.get('action');
    const entity = searchParams.get('entity');
    const userIdFilter = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};
    
    if (action) where.action = action;
    if (entity) where.entity = entity;
    if (userIdFilter) where.userId = userIdFilter;
    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      where.createdAt = { gte: new Date(startDate) };
    } else if (endDate) {
      where.createdAt = { lte: new Date(endDate) };
    }

    // Get audit logs with pagination
    const [auditLogs, totalCount] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    const response = {
      auditLogs,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        action,
        entity,
        userId: userIdFilter,
        startDate,
        endDate,
      },
    };

    // Log the action (but don't create infinite loop)
    if (action !== 'READ' || entity !== 'AuditLog') {
      await AuthService.logAuditEvent(
        userId,
        'READ',
        'AuditLog',
        'query',
        null,
        { filters: where, pagination: { page, limit } },
        request.ip || 'unknown',
        request.headers.get('user-agent') || 'unknown'
      );
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Get audit logs error:', error);
    return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 });
  }
}

// Get audit log statistics
export async function POST(request: NextRequest) {
  try {
    // Get user info from headers (set by middleware)
    const userRole = request.headers.get('x-user-role') as UserRole;
    const userId = request.headers.get('x-user-id');

    if (!userId || !userRole) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Only Admin can view audit statistics
    if (!AuthService.hasPermission(userRole, UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { startDate, endDate, groupBy = 'day' } = body;

    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default: last 30 days
    const end = endDate ? new Date(endDate) : new Date();

    // Get statistics
    const [
      actionStats,
      entityStats,
      userStats,
      timelineStats,
    ] = await Promise.all([
      // Actions distribution
      prisma.auditLog.groupBy({
        by: ['action'],
        _count: { action: true },
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      }),

      // Entities distribution
      prisma.auditLog.groupBy({
        by: ['entity'],
        _count: { entity: true },
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      }),

      // Most active users
      prisma.auditLog.groupBy({
        by: ['userId'],
        _count: { userId: true },
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        orderBy: {
          _count: {
            userId: 'desc',
          },
        },
        take: 10,
      }),

      // Timeline data (daily activity)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC(${groupBy}, "createdAt") as period,
          COUNT(*) as activity_count,
          COUNT(DISTINCT "userId") as unique_users
        FROM "AuditLog" 
        WHERE "createdAt" >= ${start} AND "createdAt" <= ${end}
        GROUP BY DATE_TRUNC(${groupBy}, "createdAt")
        ORDER BY period ASC
      `,
    ]);

    // Get user details for top users
    const userIds = userStats.map(stat => stat.userId);
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    // Combine user stats with user details
    const userStatsWithDetails = userStats.map(stat => {
      const user = users.find(u => u.id === stat.userId);
      return {
        ...stat,
        user,
      };
    });

    const statistics = {
      period: {
        startDate: start,
        endDate: end,
        groupBy,
      },
      actions: actionStats.map(stat => ({
        action: stat.action,
        count: stat._count.action,
      })),
      entities: entityStats.map(stat => ({
        entity: stat.entity,
        count: stat._count.entity,
      })),
      topUsers: userStatsWithDetails,
      timeline: timelineStats,
      summary: {
        totalActions: actionStats.reduce((sum, stat) => sum + stat._count.action, 0),
        uniqueUsers: userStats.length,
        mostActiveAction: actionStats.reduce((max, stat) => 
          stat._count.action > (max?._count?.action || 0) ? stat : max, null
        )?.action,
        mostActiveEntity: entityStats.reduce((max, stat) => 
          stat._count.entity > (max?._count?.entity || 0) ? stat : max, null
        )?.entity,
      },
    };

    // Log the action
    await AuthService.logAuditEvent(
      userId,
      'READ',
      'AuditLog',
      'statistics',
      null,
      { period: { startDate: start, endDate: end }, groupBy },
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(statistics);
  } catch (error) {
    console.error('Get audit statistics error:', error);
    return NextResponse.json({ error: 'Failed to fetch audit statistics' }, { status: 500 });
  }
}
