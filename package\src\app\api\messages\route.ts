import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { messageSchema } from '@/lib/validation';
import { AuditService } from '@/lib/audit';

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type'); // 'sent', 'received', 'all'
    const unreadOnly = searchParams.get('unreadOnly') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause based on message type
    let where: any = {};
    
    switch (type) {
      case 'sent':
        where.senderId = userId;
        break;
      case 'received':
        where.OR = [
          { recipientId: userId },
          { recipientRole: userRole, recipientId: null } // Role-based messages
        ];
        break;
      default: // 'all'
        where.OR = [
          { senderId: userId },
          { recipientId: userId },
          { recipientRole: userRole, recipientId: null }
        ];
    }

    if (unreadOnly && type !== 'sent') {
      where.isRead = false;
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          recipient: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          parent: {
            select: {
              id: true,
              subject: true
            }
          },
          replies: {
            select: {
              id: true,
              subject: true,
              createdAt: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 3
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit,
      }),
      prisma.message.count({ where })
    ]);

    // Get unread count for received messages
    const unreadCount = await prisma.message.count({
      where: {
        OR: [
          { recipientId: userId },
          { recipientRole: userRole, recipientId: null }
        ],
        isRead: false
      }
    });

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    });
  } catch (error) {
    console.error('Failed to fetch messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    const userRole = request.headers.get('x-user-role');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' }, 
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate input
    const validationResult = messageSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Validate recipient
    if (!data.recipientId && !data.recipientRole) {
      return NextResponse.json(
        { error: 'Either recipientId or recipientRole must be provided' },
        { status: 400 }
      );
    }

    // Check if recipient exists (for direct messages)
    if (data.recipientId) {
      const recipient = await prisma.user.findUnique({
        where: { id: data.recipientId }
      });
      
      if (!recipient) {
        return NextResponse.json(
          { error: 'Recipient not found' },
          { status: 404 }
        );
      }
    }

    const message = await prisma.message.create({
      data: {
        senderId: userId,
        recipientId: data.recipientId,
        recipientRole: data.recipientRole,
        subject: data.subject,
        content: data.content,
        messageType: data.messageType,
        priority: data.priority,
        parentId: data.parentId,
        attachments: data.attachments,
        scheduledFor: data.scheduledFor ? new Date(data.scheduledFor) : null,
        sentAt: data.scheduledFor ? null : new Date(),
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        recipient: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    // Create notification for recipient(s)
    if (data.recipientId) {
      // Direct message notification
      await prisma.notification.create({
        data: {
          userId: data.recipientId,
          type: 'MESSAGE_RECEIVED',
          title: `New message from ${message.sender.name}`,
          message: `Subject: ${data.subject}`,
          category: 'message',
          actionUrl: `/messages/${message.id}`,
          priority: data.priority === 'URGENT' ? 'HIGH' : 'NORMAL'
        }
      });
    } else if (data.recipientRole) {
      // Role-based message notifications
      const recipients = await prisma.user.findMany({
        where: { 
          role: data.recipientRole,
          isActive: true
        },
        select: { id: true }
      });

      const notifications = recipients.map(recipient => ({
        userId: recipient.id,
        type: 'MESSAGE_RECEIVED' as const,
        title: `New broadcast message from ${message.sender.name}`,
        message: `Subject: ${data.subject}`,
        category: 'message',
        actionUrl: `/messages/${message.id}`,
        priority: data.priority === 'URGENT' ? 'HIGH' as const : 'NORMAL' as const
      }));

      await prisma.notification.createMany({
        data: notifications
      });
    }

    // Log the action
    await AuditService.log(
      userId,
      'CREATE',
      'Message',
      message.id,
      null,
      message,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(message, { status: 201 });
  } catch (error) {
    console.error('Failed to create message:', error);
    return NextResponse.json(
      { error: 'Failed to create message' }, 
      { status: 500 }
    );
  }
}
