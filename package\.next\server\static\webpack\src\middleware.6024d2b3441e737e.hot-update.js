"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/auth */ \"(middleware)/./src/lib/auth.ts\");\n\n\n// Define protected routes and their required roles\nconst protectedRoutes = {\n    \"/\": [\n        \"ADMIN\",\n        \"RECEPTION\"\n    ],\n    \"/class-management/teachers\": [\n        \"ADMIN\"\n    ],\n    \"/class-management/payments\": [\n        \"ADMIN\"\n    ],\n    \"/class-management/classes\": [\n        \"ADMIN\",\n        \"RECEPTION\",\n        \"TEACHER\"\n    ],\n    \"/class-management/students\": [\n        \"ADMIN\",\n        \"RECEPTION\"\n    ],\n    \"/class-management/cabinets\": [\n        \"ADMIN\",\n        \"RECEPTION\"\n    ],\n    \"/class-management/timetable\": [\n        \"ADMIN\",\n        \"RECEPTION\",\n        \"TEACHER\"\n    ],\n    \"/api/\": [\n        \"ADMIN\",\n        \"RECEPTION\",\n        \"TEACHER\"\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    \"/authentication/login\",\n    \"/authentication/register\",\n    \"/api/auth/login\",\n    \"/api/auth/register\",\n    \"/api/auth/refresh\"\n];\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/static/\") || pathname.includes(\".\") || pathname === \"/favicon.ico\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Allow public routes (including authentication pages)\n    if (publicRoutes.some((route)=>pathname.startsWith(route))) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Temporarily disable middleware for authentication pages to debug\n    if (pathname.startsWith(\"/authentication/\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get token from Authorization header or cookies\n    const authHeader = request.headers.get(\"authorization\");\n    const token = authHeader?.replace(\"Bearer \", \"\") || request.cookies.get(\"accessToken\")?.value;\n    if (!token) {\n        // Redirect to login for web pages\n        if (!pathname.startsWith(\"/api/\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/authentication/login\", request.url));\n        }\n        // Return 401 for API routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    // Verify token\n    const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n    if (!payload) {\n        // Clear invalid token and redirect\n        const response = pathname.startsWith(\"/api/\") ? next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Invalid token\"\n        }, {\n            status: 401\n        }) : next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/authentication/login\", request.url));\n        response.cookies.delete(\"accessToken\");\n        return response;\n    }\n    // Check route permissions\n    const requiredRoles = getRequiredRoles(pathname);\n    if (requiredRoles.length > 0 && !requiredRoles.includes(payload.role)) {\n        // Access denied\n        if (pathname.startsWith(\"/api/\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        // Redirect to appropriate page based on role\n        const redirectUrl = getRedirectUrlForRole(payload.role);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(redirectUrl, request.url));\n    }\n    // Add user info to request headers for API routes\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    response.headers.set(\"x-user-id\", payload.userId);\n    response.headers.set(\"x-user-email\", payload.email);\n    response.headers.set(\"x-user-role\", payload.role);\n    response.headers.set(\"x-user-name\", payload.name);\n    return response;\n}\nfunction getRequiredRoles(pathname) {\n    // Check exact matches first\n    for (const [route, roles] of Object.entries(protectedRoutes)){\n        if (pathname === route || route.endsWith(\"/\") && pathname.startsWith(route)) {\n            return roles;\n        }\n    }\n    // Check if it's an API route\n    if (pathname.startsWith(\"/api/\")) {\n        // Default API access for authenticated users\n        return [\n            \"ADMIN\",\n            \"RECEPTION\",\n            \"TEACHER\"\n        ];\n    }\n    // Default: require authentication but no specific role\n    return [];\n}\nfunction getRedirectUrlForRole(role) {\n    switch(role){\n        case \"ADMIN\":\n            return \"/\"; // Dashboard\n        case \"RECEPTION\":\n            return \"/class-management/classes\"; // Classes page\n        case \"TEACHER\":\n            return \"/class-management/classes\"; // Classes page\n        case \"STUDENT\":\n            return \"/student/dashboard\"; // Student dashboard (to be implemented)\n        default:\n            return \"/authentication/login\";\n    }\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!_next/static|_next/image|favicon.ico|public/).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});