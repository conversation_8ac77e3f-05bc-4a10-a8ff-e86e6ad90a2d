"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated\n    const isAuthenticated = !!user;\n    // Role hierarchy for permission checking\n    const roleHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.STUDENT]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.TEACHER]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.RECEPTION]: 2,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN]: 3\n    };\n    // Check if user has required permission\n    const hasPermission = (requiredRole)=>{\n        if (!user) return false;\n        return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n    };\n    // Login function\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                setUser(data.user);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error occurred\"\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            // Redirect to login page using Next.js navigation\n            if (true) {\n                window.location.href = \"/authentication/login\";\n            }\n        }\n    };\n    // Refresh token function\n    const refreshToken = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.user);\n                    return true;\n                }\n            }\n            // If refresh fails, logout user\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Get current user on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getCurrentUser = async ()=>{\n            try {\n                const response = await fetch(\"/api/auth/me\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    }\n                } else if (response.status === 401) {\n                    // Try to refresh token\n                    const refreshSuccess = await refreshToken();\n                    if (!refreshSuccess) {\n                        // Don't redirect here, let ProtectedRoute handle it\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Get current user error:\", error);\n                setUser(null);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getCurrentUser();\n    }, []);\n    // Auto-refresh token before expiry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(async ()=>{\n            await refreshToken();\n        }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        refreshToken,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Improved-CRM\\\\package\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"l8KTfcQ0b0tPgqlKoCac6dKortU=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});