var z=Object.defineProperty;var o=(s,e)=>z(s,"name",{value:e,configurable:!0});import{r as G}from"./get-pipe-path-BHW2eJdv.mjs";import d from"node:module";import h from"node:path";import{fileURLToPath as F}from"node:url";import{parseTsconfig as H,getTsconfig as X,createFilesMatcher as K,createPathsMatcher as V}from"get-tsconfig";import Y from"node:fs";import{b as Z,i as q,a as ee}from"./index-DGv_vkxZ.mjs";import{p as U}from"./client-BQVF1NaW.mjs";const W=o(s=>{if(!s.startsWith("data:text/javascript,"))return;const e=s.indexOf("?");if(e===-1)return;const n=new URLSearchParams(s.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),$=o(s=>{const e=W(s);return e&&(d._cache[e]=d._cache[s],delete d._cache[s],s=e),s},"interopCjsExports"),se=o(s=>{const e=s.indexOf(":");if(e!==-1)return s.slice(0,e)},"getScheme"),L=o(s=>s[0]==="."&&(s[1]==="/"||s[1]==="."||s[2]==="/"),"isRelativePath"),y=o(s=>L(s)||h.isAbsolute(s),"isFilePath"),te=o(s=>{if(y(s))return!0;const e=se(s);return e&&e!=="node"},"requestAcceptsQuery"),x="file://",R=/\.([cm]?ts|[tj]sx)($|\?)/,ne=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,ae=/\.json($|\?)/,_=/\/(?:$|\?)/,re=/^(?:@[^/]+\/)?[^/\\]+$/,D=`${h.sep}node_modules${h.sep}`;let S,E,w=!1;const N=o(s=>{let e=null;if(s){const a=h.resolve(s);e={path:a,config:H(a)}}else{try{e=X()}catch{}if(!e)return}S=K(e),E=V(e),w=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),M=o(s=>Array.from(s).length>0?`?${s.toString()}`:"","urlSearchParamsStringify"),oe=`
//# sourceMappingURL=data:application/json;base64,`,C=o(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),v=o(({code:s,map:e})=>s+oe+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),ce=[".cts",".mts",".ts",".tsx",".jsx"],ie=[".js",".cjs",".mjs"],A=[".ts",".tsx",".jsx"],T=o((s,e,a,n)=>{const t=Object.getOwnPropertyDescriptor(s,e);t?.set?s[e]=a:(!t||t.configurable)&&Object.defineProperty(s,e,{value:a,enumerable:t?.enumerable||n?.enumerable,writable:n?.writable??(t?t.writable:!0),configurable:n?.configurable??(t?t.configurable:!0)})},"safeSet"),le=o((s,e,a)=>{const n=e[".js"],t=o((r,c)=>{if(s.enabled===!1)return n(r,c);const[l,f]=c.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==a)return n(r,c);r.id.startsWith("data:text/javascript,")&&(r.path=h.dirname(l)),U?.send&&U.send({type:"dependency",path:l});const u=ce.some(m=>l.endsWith(m)),P=ie.some(m=>l.endsWith(m));if(!u&&!P)return n(r,l);let p=Y.readFileSync(l,"utf8");if(l.endsWith(".cjs")){const m=Z(c,p);m&&(p=C()?v(m):m.code)}else if(u||q(p)){const m=ee(p,c,{tsconfigRaw:S?.(l)});p=C()?v(m):m.code}r._compile(p,l)},"transformer");T(e,".js",t);for(const r of A)T(e,r,t,{enumerable:!a,writable:!0,configurable:!0});return T(e,".mjs",t,{writable:!0,configurable:!0}),()=>{e[".js"]===t&&(e[".js"]=n);for(const r of[...A,".mjs"])e[r]===t&&delete e[r]}},"createExtensions"),fe=o(s=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),_.test(e)){let a=h.join(e,"index.js");e.startsWith("./")&&(a=`./${a}`);try{return s(a)}catch{}}try{return s(e)}catch(a){const n=a;if(n.code==="MODULE_NOT_FOUND")try{return s(`${e}${h.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),Q=[".js",".json"],I=[".ts",".tsx",".jsx"],me=[...I,...Q],he=[...Q,...I],g=Object.create(null);g[".js"]=[".ts",".tsx",".js",".jsx"],g[".jsx"]=[".tsx",".ts",".jsx",".js"],g[".cjs"]=[".cts"],g[".mjs"]=[".mts"];const k=o(s=>{const e=s.split("?"),a=e[1]?`?${e[1]}`:"",[n]=e,t=h.extname(n),r=[],c=g[t];if(c){const f=n.slice(0,-t.length);r.push(...c.map(i=>f+i+a))}const l=!(s.startsWith(x)||y(n))||n.includes(D)||n.includes("/node_modules/")?he:me;return r.push(...l.map(f=>n+f+a)),r},"mapTsExtensions"),b=o((s,e,a)=>{if(_.test(e)||!a&&!w)return;const n=k(e);if(n)for(const t of n)try{return s(t)}catch(r){const{code:c}=r;if(c!=="MODULE_NOT_FOUND"&&c!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw r}},"resolveTsFilename"),pe=o((s,e)=>a=>{if(y(a)){const n=b(s,a,e);if(n)return n}try{return s(a)}catch(n){const t=n;if(t.code==="MODULE_NOT_FOUND"){if(typeof t.path=="string"&&t.path.endsWith(`${h.sep}package.json`)){const c=t.message.match(/^Cannot find module '([^']+)'$/);if(c){const f=c[1],i=b(s,f,e);if(i)return i}const l=t.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(l){const f=l[1],i=b(s,f,e);if(i)return i}}const r=b(s,a,e);if(r)return r}throw t}},"createTsExtensionResolver"),J="at cjsPreparseModuleExports (node:internal",de=o(s=>{const e=s.stack.split(`
`).slice(1);return e[1].includes(J)||e[2].includes(J)},"isFromCjsLexer"),ue=o((s,e)=>{const a=s.split("?"),n=new URLSearchParams(a[1]);if(e?.filename){const t=W(e.filename);let r;if(t){const f=t.split("?"),i=f[0];r=f[1],e.filename=i,e.path=h.dirname(i),e.paths=d._nodeModulePaths(e.path),d._cache[i]=e}r||(r=e.filename.split("?")[1]);const l=new URLSearchParams(r).get("namespace");l&&n.append("namespace",l)}return[a[0],n,(t,r)=>(h.isAbsolute(t)&&!t.endsWith(".json")&&!t.endsWith(".node")&&!(r===0&&de(new Error))&&(t+=M(n)),t)]},"preserveQuery"),Pe=o((s,e,a)=>{if(s.startsWith(x)&&(s=F(s)),E&&!y(s)&&!e?.filename?.includes(D)){const n=E(s);for(const t of n)try{return a(t)}catch{}}return a(s)},"resolveTsPaths"),ge=o((s,e,a)=>(n,t,...r)=>{if(s.enabled===!1)return e(n,t,...r);n=$(n);const[c,l,f]=ue(n,t);if((l.get("namespace")??void 0)!==a)return e(n,t,...r);let i=o(P=>e(P,t,...r),"nextResolveSimple");i=pe(i,!!(a||t?.filename&&R.test(t.filename))),i=fe(i);const u=Pe(c,t,i);return f(u,r.length)},"createResolveFilename"),B=o((s,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return s.startsWith(".")?((typeof e=="string"&&e.startsWith(x)||e instanceof URL)&&(e=F(e)),h.resolve(h.dirname(e),s)):s},"resolveContext"),je=o(s=>{const{sourceMapsEnabled:e}=process,a={enabled:!0};N(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=d._resolveFilename,t=ge(a,n,s?.namespace);d._resolveFilename=t;const r=le(a,d._extensions,s?.namespace),c=o(()=>{e===!1&&process.setSourceMapsEnabled(!1),a.enabled=!1,d._resolveFilename===t&&(d._resolveFilename=n),r()},"unregister");if(s?.namespace){const l=o((i,u)=>{const P=B(i,u),[p,m]=P.split("?"),j=new URLSearchParams(m);return s.namespace&&!p.startsWith("node:")&&j.set("namespace",s.namespace),G(p+M(j))},"scopedRequire");c.require=l;const f=o((i,u,P)=>{const p=B(i,u),[m,j]=p.split("?"),O=new URLSearchParams(j);return s.namespace&&!m.startsWith("node:")&&O.set("namespace",s.namespace),t(m+M(O),module,!1,P)},"scopedResolve");c.resolve=f,c.unregister=c}return c},"register");export{$ as a,ae as b,ne as c,S as d,v as e,x as f,te as g,E as h,re as i,_ as j,L as k,N as l,k as m,w as n,je as r,R as t};
